import { ObjectKey } from "../interfaces/common";
import { SaleItem, SalePackage } from "../interfaces/payment";

export const LIST_COUNTRY = [
    {
        langCode: 'ja',
        name: 'Japan'
    },
    {
        langCode: 'vi',
        name: 'Vietnam'
    },
    {
        langCode: 'id',
        name: 'Indonesia'
    },
    {
        langCode: 'zh',
        name: 'China'
    },
    {
        langCode: 'ru',
        name: 'Russia'
    }
];

export const examJoptFee: SalePackage<SaleItem | undefined> = {
    name: "<PERSON><PERSON> đăng ký thi",
    productId: "mytest_jopt",
    id: "mytest_jopt",
    prices: {
        "vi": {
            "price": 1970000,
            "currency": "VND",
            "symbol": "₫"
        },
        "en": {
            "price": 50,
            "currency": "USD",
            "symbol": "$"
        }
    },
    discountPercent: 0,
    sale: undefined
}

export const COUNTRY_BANK: ObjectKey<any> = {
    vi: [{
        img: 'ic_logo_acb.svg',
        bankName: 'ACB',
        accountName: 'CONG TY CP CONG NGHE EUP',
        accountNumber: '********',
    }],
    ru: [{
        bankName: 'T.bank',
        accountName: 'Нгуен Дык Тунг',
        accountNumber: '****************',
    }],
    zh: [{
        bankName: 'Alipay',
        accountName: 'thanhvan',
        accountNumber: '84-*********',
    }],
    'zh-Hant': [{
        bankName: 'Alipay',
        accountName: 'thanhvan',
        accountNumber: '84-*********',
    }],
    id: [{
        bankName: 'NAMA BANK BNI',
        accountName: 'YUSUF BAHTIAR',
        accountNumber: '**********',
    }],
    ja: [
        {
            bankName: 'Yucho',
            accountName: 'タケハラ　ユウタ',
            accountNumber: '10980-********',
            branchCode: '098'
        },
        {
            bankName: 'PayPay',
            accountName: 'カ）コーディ',
            accountNumber: 'Futsu 7705542',
            branchCode: '005'
        }
    ]
}