import { Component, EventEmitter, Input, Output } from '@angular/core';
import { SelectOption } from '../../../common/interfaces/common';
import { SvgIconComponent } from "../svg-icon/svg-icon.component";

@Component({
    selector: 'app-select',
    standalone: true,
    imports: [SvgIconComponent],
    templateUrl: './select.component.html',
    styleUrl: './select.component.css'
})
export class SelectComponent {

    isDropdown: boolean = false;
    selected: number = 0;

    @Input() list: SelectOption[] = [];
    @Output() selectValue = new EventEmitter();

    toggleDropdownGender() {
        this.isDropdown = !this.isDropdown;
    }

    handleGenChange(idx: number, e: Event) {
        e.stopPropagation();
        this.selected = idx;
        this.isDropdown = false;
        this.selectValue.emit(this.list[this.selected].value);
    }
}
