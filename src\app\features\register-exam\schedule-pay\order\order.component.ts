import { Component, Input } from '@angular/core';
import { RoundPricePipe } from '../../../../common/pipes/roundPrice.pipe';
import { SaleItem, SalePackage } from '../../../../common/interfaces/payment';

@Component({
    standalone: true,
    imports: [
        RoundPricePipe
    ],
    selector: 'app-order',
    templateUrl: './order.component.html',
    styleUrl: './order.component.scss'
})
export class OrderComponent {

    @Input() lang: string = 'en';
    @Input() packDetail!: SalePackage<SaleItem | undefined>;
    @Input() total: string = '';
    @Input() saved: boolean = false;

    constructor() {}
    
}
