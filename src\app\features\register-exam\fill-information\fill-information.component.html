<div class="container max-w-[1120px] mx-auto grid grid-cols-3 gap-5 my-5">
    <div class="col-span-2">
        <div class="p-4 bg-bg-white rounded-2xl">
            <div class="font-semibold leading-6 mb-4">
                <span i18n="@@txt_personal_infor">Personal information</span>
                <span class="text-lg text-ct-error-primary">*</span>
            </div>
            <form class="grid grid-cols-2 gap-3" [formGroup]="registerExam">
                <div>
                    <label for="name" class="font-body standard-txt flex items-center gap-1 mb-1">
                        <span class="text-ct-primary text-sm leading-5" i18n="@@txt_fullname">Fullname</span>
                    </label>
                    <div class="input input-lg flex item-center"
                        [class.input-err]="submitted && registerExam.controls['name'].errors"
                        [class.input-success]="submitted && !registerExam.controls['name'].errors">
                        <input #firstInput formControlName="name" type="text" class="w-full outline-none bg-transparent" id="name" placeholder="Nguyễn Văn A">
                    </div>
                    <div class="flex items-center gap-1 mt-1">
                        @if(submitted && registerExam.controls['name'].errors){
                            <app-svg-icon name="warning" fill="--ct-error-primary" [width]="16" [height]="16"></app-svg-icon>
                            @if (registerExam.controls['name'].errors['required']) {
                                <span class="text-[12px] text-ct-error-primary" i18n="@@txt_field_blank">This field cannot be blank</span>
                            }@else if (registerExam.controls['name'].errors['minLength']) {
                                <span class="text-[12px] text-ct-error-primary" i18n="@@txt_err_name_min">Name must be at least 2 characters</span>
                            }@else if (registerExam.controls['name'].errors['maxLength']) {
                                <span class="text-[12px] text-ct-error-primary" i18n="@@txt_err_name_max">Name no more than 200 characters</span>
                            }
                        }
                    </div>
                </div>
                <div>
                    <label class="font-body standard-txt flex items-center gap-1 mb-1">
                        <span class="text-ct-primary text-sm leading-5" i18n="@@txt_date_of_bird">Date of bird</span>
                    </label>
                    <div class="input input-lg flex item-center"
                        [class.input-err]="submitted && registerExam.controls['dateOfBird'].errors"
                        [class.input-success]="submitted && !registerExam.controls['dateOfBird'].errors">
                        <input #firstInput formControlName="dateOfBird" type="date" class="w-full outline-none bg-transparent" id="dateOfBird" placeholder="Nguyễn Văn A">
                    </div>
                    <div class="flex items-center gap-1 mt-1">
                        @if(submitted && registerExam.controls['dateOfBird'].errors){
                            <app-svg-icon name="warning" fill="--ct-error-primary" [width]="16" [height]="16"></app-svg-icon>
                            <span class="text-[12px] text-ct-error-primary" i18n="@@txt_field_blank">This field cannot be blank</span>
                        }
                    </div>
                </div>
                <div>
                    <label class="font-body standard-txt flex items-center gap-1 mb-1">
                        <span class="text-ct-primary text-sm leading-5" i18n="@@txt_gender">Genger</span>
                    </label>
                    <div class="select pr-[10px]"
                        [class.input-err]="submitted && registerExam.controls['gender'].errors"
                        [class.input-success]="submitted && !registerExam.controls['gender'].errors">
                        <select class="w-full" formControlName="gender">
                            @for (item of dataInit.GENDER; track $index) {
                                <option [disabled]="!$index" [ngValue]="item.value">{{ item.label }}</option>
                            }
                        </select>
                    </div>
                    <div class="flex items-center gap-1 mt-1">
                        @if(submitted && registerExam.controls['gender'].errors){
                            <app-svg-icon name="warning" fill="--ct-error-primary" [width]="16" [height]="16"></app-svg-icon>
                            <span class="text-[12px] text-ct-error-primary" i18n="@@txt_field_blank">This field cannot be blank</span>
                        }
                    </div>
                </div>
                <div>
                    <label for="cccd" class="font-body standard-txt flex items-center gap-1 mb-1">
                        <span class="text-ct-primary text-sm leading-5" i18n="@@txt_id_passport">ID card / passport</span>
                    </label>
                    <div class="input input-lg flex item-center"
                        [class.input-err]="submitted && registerExam.controls['cccd'].errors"
                        [class.input-success]="submitted && !registerExam.controls['cccd'].errors">
                        <input #firstInput formControlName="cccd" type="text" class="w-full outline-none bg-transparent" id="cccd" i18n-placeholder="@@txt_enter_infor" placeholder="Enter information">
                    </div>
                    <div class="flex items-center gap-1 mt-1">
                        @if(submitted && registerExam.controls['cccd'].errors){
                            <app-svg-icon name="warning" fill="--ct-error-primary" [width]="16" [height]="16"></app-svg-icon>
                            <span class="text-[12px] text-ct-error-primary" i18n="@@txt_field_blank">This field cannot be blank</span>
                        }
                    </div>
                </div>
    
                <div>
                    <label for="email" class="font-body standard-txt flex items-center gap-1">
                        <span class="text-ct-primary text-sm">Email</span>
                    </label>
                    <div class="input input-lg flex items-center"
                        [class.input-err]="submitted && registerExam.controls['email'].errors"
                        [class.input-success]="submitted && !registerExam.controls['email'].errors">
                        <input #firstInput formControlName="email" type="email" class="w-full outline-none bg-transparent" id="email" placeholder="<EMAIL>">
                    </div>
                    <div class="flex items-center gap-1 mt-1">
                        @if(submitted && registerExam.controls['email'].errors){
                            <app-svg-icon name="warning" fill="--ct-error-primary" [width]="16" [height]="16"></app-svg-icon>
                            @if(registerExam.controls['email'].errors['email']){
                                <span class="text-[12px] text-ct-error-primary" i18n="@@txt_invalid_email">Invalid email</span>
                            }@else if(registerExam.controls['email'].errors['required']){
                                <span class="text-[12px] text-ct-error-primary" i18n="@@txt_field_blank">This field cannot be blank</span>
                            }
                        }
                    </div>
                </div>
    
                <div>
                    <label for="phone" class="font-body standard-txt flex items-center gap-1">
                        <span class="text-ct-primary text-sm" i18n="@@txt_phone_number">Phone number</span>
                    </label>
                    <div class="flex items-center gap-2">
                        <app-select [list]="dataInit.FLAG_PHONES" (selectValue)="handleFlagPhoneChange($event)" />
                        <div class="input input-lg flex items-center grow"
                            [class.input-err]="submitted && registerExam.controls['phone'].errors"
                            [class.input-success]="submitted && !registerExam.controls['phone'].errors">
                            <span>{{ startPhone }}</span>
                            <input #firstInput formControlName="phone" type="text" class="w-full outline-none bg-transparent" id="phone" placeholder="912******">
                        </div>
                    </div>
                    <div class="flex items-center gap-1 mt-1">
                        @if(submitted && registerExam.controls['phone'].errors){
                            <app-svg-icon name="warning" fill="--ct-error-primary" [width]="16" [height]="16"></app-svg-icon>
                            <span class="text-[12px] text-ct-error-primary" i18n="@@txt_field_blank">This field cannot be blank</span>
                        }
                    </div>
                </div>
    
                <div>
                    <label for="address" class="font-body standard-txt flex items-center gap-1">
                        <span class="text-ct-primary text-sm" i18n="@@txt_address">Address</span>
                    </label>
                    <div class="input input-lg flex items-center"
                        [class.input-err]="submitted && registerExam.controls['address'].errors"
                        [class.input-success]="submitted && !registerExam.controls['address'].errors">
                        <input #firstInput formControlName="address" type="text" class="w-full outline-none bg-transparent" id="address" placeholder="Số 88 đường A, quận B, tp C">
                    </div>
                    <div class="flex items-center gap-1 mt-1">
                        @if(submitted && registerExam.controls['address'].errors){
                            <app-svg-icon name="warning" fill="--ct-error-primary" [width]="16" [height]="16"></app-svg-icon>
                            <span class="text-[12px] text-ct-error-primary" i18n="@@txt_field_blank">This field cannot be blank</span>
                        }
                    </div>
                </div>
    
                <div>
                    <label for="job" class="font-body standard-txt flex items-center gap-1">
                        <span class="text-ct-primary text-sm" i18n="@@txt_occupation">Occupation</span>
                    </label>
                    <div class="select pr-[10px]"
                        [class.input-err]="submitted && registerExam.controls['job'].errors"
                        [class.input-success]="submitted && !registerExam.controls['job'].errors">
                        <select class="w-full" formControlName="job">
                            @for (item of dataInit.JOBS; track $index) {
                                <option [disabled]="!$index" [ngValue]="item.value">{{ item.label }}</option>
                            }
                        </select>
                    </div>
                    <div class="flex items-center gap-1 mt-1">
                        @if(submitted && registerExam.controls['job'].errors){
                            <app-svg-icon name="warning" fill="--ct-error-primary" [width]="16" [height]="16"></app-svg-icon>
                            <span class="text-[12px] text-ct-error-primary" i18n="@@txt_field_blank">This field cannot be blank</span>
                        }
                    </div>
                </div>
    
            </form>
        </div>
    
        <div class="p-4 bg-bg-white rounded-2xl mt-4">
            <div class="font-semibold leading-6 mb-4">
                <span i18n="@@txt_select_doc">Select document type</span>
            </div>
            <div class="grid grid-cols-2 gap-4">
                <div>
                    <div class="mb-3 text-sm font-medium" i18n="@@txt_front_photo">Front photo</div>
                    <div class="relative w-full flex items-center justify-center border rounded-lg border-dashed bg-bg-primary aspect-[340/192]">
                        @if(imageCccdFront) {
                            <img class="rounded-lg pointer w-full h-full" [class.opacity-50]="loadingCccdFront" [src]="imageCccdFrontUrl" alt="Cccd">
                            @if(loadingCccdFront) {
                                <div class="loader absolute"></div>
                            }
                        } @else {
                            <div class="pointer bg-bg-info-solid text-ct-white flex w-40 justify-center items-center gap-2 p-2 rounded-lg" (click)="preCrop('cccd-front', 85.6/53.98);ngImageInput.click()">
                                <img class="w-5 h-5" src="/images/icons/camera-02.svg" alt="Migii Icon">
                                <span class="text-xs" i18n="@@txt_upload_photo">Upload photo</span>
                            </div>
                        }
                    </div>
                </div>
    
                <div>
                    <div class="mb-3 text-sm font-medium" i18n="@@txt_back_photo">Back photo</div>
                    <div class="relative w-full flex items-center justify-center border rounded-lg border-dashed bg-bg-primary aspect-[340/192]">
                        @if(imageCccdBack) {
                            <img class="rounded-lg pointer w-full h-full" [class.opacity-50]="loadingCccdBack" [src]="imageCccdBackUrl" alt="Cccd">
                            @if(loadingCccdBack) {
                                <div class="loader absolute"></div>
                            }
                        } @else {
                            <div class="pointer bg-bg-info-solid text-ct-white flex w-40 justify-center items-center gap-2 p-2 rounded-lg" (click)="preCrop('cccd-back', 85.6/53.98);ngImageInput.click()">
                                <img class="w-5 h-5" src="/images/icons/camera-02.svg" alt="Migii Icon">
                                <span class="text-xs" i18n="@@txt_upload_photo">Upload photo</span>
                            </div>
                        }
                    </div>
                </div>
            </div>
    
            <div class="text-ct-error-secondary text-sm mt-2" i18n="@@txt_">*We will collect your CCCD/Passport information and guarantee your confidentiality.</div>
        </div>
    
        <div class="p-4 bg-bg-white rounded-2xl mt-4">
            <div class="font-semibold leading-6 mb-4">
                <span i18n="@@txt_exam_info">Exam Information</span>
                <span class="text-lg text-ct-error-primary">*</span>
            </div>
    
            <form class="grid grid-cols-2 gap-3" [formGroup]="inforExam">
                <div class="col-span-2">
                    <label class="font-body standard-txt flex items-center gap-1 mb-1">
                        <span class="text-ct-primary text-sm leading-5" i18n="@@txt_purpose">Purpose</span>
                    </label>
                    <div class="select pr-[10px]"
                        [class.input-err]="submitted && inforExam.controls['test_purpose'].errors"
                        [class.input-success]="submitted && !inforExam.controls['test_purpose'].errors">
                        <select class="w-full"  formControlName="test_purpose">
                            @for (item of dataInit.TEST_PURPOSES; track $index) {
                                <option [disabled]="!$index" [ngValue]="item.value">{{ item.label }}</option>
                            }
                        </select>
                    </div>
                    <div class="flex items-center gap-1 mt-1">
                        @if(submitted && inforExam.controls['test_purpose'].errors){
                            <app-svg-icon name="warning" fill="--ct-error-primary" [width]="16" [height]="16"></app-svg-icon>
                            <span class="text-[12px] text-ct-error-primary" i18n="@@txt_field_blank">This field cannot be blank</span>
                        }
                    </div>
                </div>
        
                <div>
                    <label class="font-body standard-txt flex items-center gap-1 mb-1">
                        <span class="text-ct-primary text-sm leading-5" i18n="@@txt_level_jopt_target">JOPT level target</span>
                    </label>
                    <div class="select pr-[10px]"
                        [class.input-err]="submitted && inforExam.controls['jopt_target'].errors"
                        [class.input-success]="submitted && !inforExam.controls['jopt_target'].errors">
                        <select class="w-full"  formControlName="jopt_target">
                            @for (item of dataInit.LEVEL_JOPTS; track $index) {
                                <option [disabled]="!$index" [ngValue]="item.value">{{ item.label }}</option>
                            }
                        </select>
                    </div>
                    <div class="flex items-center gap-1 mt-1">
                        @if(submitted && inforExam.controls['jopt_target'].errors){
                            <app-svg-icon name="warning" fill="--ct-error-primary" [width]="16" [height]="16"></app-svg-icon>
                            <span class="text-[12px] text-ct-error-primary" i18n="@@txt_field_blank">This field cannot be blank</span>
                        }
                    </div>
                </div>
        
                <div>
                    <label class="font-body standard-txt flex items-center gap-1 mb-1">
                        <span class="text-ct-primary text-sm leading-5" i18n="@@txt_how_you_know_jopt">How do you know JOPT?</span>
                    </label>
                    <div class="select pr-[10px]"
                        [class.input-err]="submitted && inforExam.controls['referral_source'].errors"
                        [class.input-success]="submitted && !inforExam.controls['referral_source'].errors">
                        <select class="w-full"  formControlName="referral_source">
                            @for (item of dataInit.SOCIAL_JOPTS; track $index) {
                                <option [disabled]="!$index" [ngValue]="item.value">{{ item.label }}</option>
                            }
                        </select>
                    </div>
                    <div class="flex items-center gap-1 mt-1">
                        @if(submitted && inforExam.controls['referral_source'].errors){
                            <app-svg-icon name="warning" fill="--ct-error-primary" [width]="16" [height]="16"></app-svg-icon>
                            <span class="text-[12px] text-ct-error-primary" i18n="@@txt_field_blank">This field cannot be blank</span>
                        }
                    </div>
                </div>
        
                <div>
                    <label class="font-body standard-txt flex items-center gap-1 mb-1">
                        <span class="text-ct-primary text-sm leading-5" i18n="@@txt_you_know_jopt">Have you ever taken the JLPT?</span>
                    </label>
                    <div class="select pr-[10px]"
                        [class.input-err]="submitted && inforExam.controls['has_taken_jlpt'].errors"
                        [class.input-success]="submitted && !inforExam.controls['has_taken_jlpt'].errors">
                        <select class="w-full"  formControlName="has_taken_jlpt">
                            @for (item of dataInit.TAKEN_JLPT; track $index) {
                                <option [disabled]="!$index" [ngValue]="item.value">{{ item.label }}</option>
                            }
                        </select>
                    </div>
                    <div class="flex items-center gap-1 mt-1">
                        @if(submitted && inforExam.controls['has_taken_jlpt'].errors){
                            <app-svg-icon name="warning" fill="--ct-error-primary" [width]="16" [height]="16"></app-svg-icon>
                            <span class="text-[12px] text-ct-error-primary" i18n="@@txt_field_blank">This field cannot be blank</span>
                        }
                    </div>
                </div>
        
                <div [class.opacity-60]="!inforExam.value['has_taken_jlpt']">
                    <label class="font-body standard-txt flex items-center gap-1 mb-1">
                        <span class="text-ct-primary text-sm leading-5" i18n="@@txt_your_jlpt">Your JLPT level</span>
                    </label>
                    <div class="select pr-[10px]"
                        [class.input-err]="submitted && inforExam.errors?.['levelJlptRequired']"
                        [class.input-success]="submitted && !inforExam.errors?.['levelJlptRequired']">
                        <select class="w-full {{ !inforExam.value['has_taken_jlpt'] ? 'pointer-events-none touch-none' : '' }}"  formControlName="level_jlpt">
                            @for (item of dataInit.LEVEL_JLPTS; track $index) {
                                <option [disabled]="!$index" [ngValue]="item.value">{{ item.label }}</option>
                            }
                        </select>
                    </div>
                    <div class="flex items-center gap-1 mt-1">
                        @if(submitted && inforExam.errors?.['levelJlptRequired']){
                            <app-svg-icon name="warning" fill="--ct-error-primary" [width]="16" [height]="16"></app-svg-icon>
                            <span class="text-[12px] text-ct-error-primary" i18n="@@txt_field_blank">This field cannot be blank</span>
                        }
                    </div>
                </div>
                <div>
                    <label class="col-span-2 font-body standard-txt flex items-center gap-1 mb-1">
                        <span class="text-ct-primary text-sm leading-5" i18n="@@txt_how_long_learn_jp">How long have you been studying japanese?</span>
                    </label>
                    <div class="grid grid-cols-2 gap-3">
                        <div>
                            <div class="input input-lg flex items-center"
                                [class.input-err]="submitted && inforExam.controls['total_years'].errors"
                                [class.input-success]="submitted && !inforExam.controls['total_years'].errors">
                                <input #firstInput formControlName="total_years" type="number" class="w-full outline-none bg-transparent" id="total_years" placeholder="**">
                                <div i18n="@@txt_year">Years</div>
                            </div>
                        </div>
                        <div>
                            <div class="input input-lg flex items-center"
                                [class.input-err]="submitted && inforExam.controls['total_months'].errors"
                                [class.input-success]="submitted && !inforExam.controls['total_months'].errors">
                                <input #firstInput formControlName="total_months" type="number" class="w-full outline-none bg-transparent" id="total_months" placeholder="**">
                                <div i18n="@@txt_month">Months</div>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center gap-1 mt-1">
                        @if(submitted && (inforExam.controls['total_years'].errors || inforExam.controls['total_months'].errors)){
                            <app-svg-icon name="warning" fill="--ct-error-primary" [width]="16" [height]="16"></app-svg-icon>
                            <span class="text-[12px] text-ct-error-primary" i18n="@@txt_field_blank">This field cannot be blank</span>
                        }
                    </div>
                </div>
            </form>
        </div>

        <div class="flex justify-end mt-5">
            <div class="pointer text-center w-[200px] p-2 rounded-lg bg-bg-brand-solid text-ct-white" i18n="@@txt_next" (click)="submit()">Next</div>
        </div>
    </div>

    <div>
        <div class="p-4 bg-bg-white rounded-2xl mb-[18px]">
            <div class="mb-4 flex items-center justify-between">
                <span class="font-semibold leading-6" i18n="@@txt_personal_infor">Personal information</span>
                <div (click)="commonService.openModal('modal-require-image-id')" class="pointer text-ct-info-secondary text-sm font-medium underline underline-offset-auto" i18n="@@txt_detail">Detail</div>
            </div>
            <div class="text-sm font-medium my-4" i18n="@@txt_id_photo">ID photo (Size 3x4cm)</div>
            @for (item of dataInit.REQUIRE_ID_IMAGES; track $index) {
                <div class="flex items-start gap-1" [class.mt-2]="$index">
                    <img class="w-[7px] h-5" src="/images/icons/ic_dot_note.svg" alt="Migii JOPT">
                    <div class="text-sm text-ct-secondary font-medium">{{ item }}</div>
                </div>
            }
            <div class="grid grid-cols-2 gap-4 mt-6">
                <div class="rounded-lg bg-bg-primary p-3">
                    <div class="text-sm font-medium text-center" i18n="@@txt_spamle_photo">Sample photo</div>
                    <div class="flex justify-end mt-2">
                        <img class=" w-[75%] aspect-[121/133] mr-2" src="/images/jopt/sample_id_image.png" alt="Sample">
                    </div>
                </div>
                <div class="flex flex-col items-center gap-2.5 p-3 border rounded-lg border-dashed bg-bg-primary">
                    <div class="text-sm font-medium text-center">
                        <span class="" i18n="@@txt_candidate_photo">Candidate photo</span>&nbsp;
                        <span class="text-[var(--bg-error-solid)]">*</span>
                    </div>
                    <img class="rounded w-[51%] mx-auto aspect-[82/107]" [src]="imageId ? imageIdUrl : '/images/jopt/default_photo.png'" alt="default_photo">
                    <div class="pointer w-max bg-bg-info-solid text-ct-white flex justify-center items-center gap-2 px-2 py-1 rounded-lg" (click)="preCrop('image-id', 3/4);ngImageInput.click()">
                        <img class="w-4 h-4" src="/images/icons/camera-02.svg" alt="Migii Icon">
                        <span class="text-xs" i18n="@@txt_upload_photo">Upload photo</span>
                    </div>
                </div>

            </div>
        </div>
        <div class="p-4 bg-bg-white rounded-2xl">
            <div class="font-semibold leading-6 mb-4" i18n="@@txt_note">Note</div> 
            @for (note of dataInit.NOTES; track note) {
                <div class="flex items-start gap-1" [class.mt-2]="$index">
                    <img class="w-[7px] h-5" src="/images/icons/ic_dot_note.svg" alt="Migii JOPT">
                    <div class="text-sm text-ct-secondary font-medium">{{ note }}</div>
                </div>
            }
            <div class="flex items-center justify-around mt-6">
                @for (item of dataInit.NOTE_IMGS; track item) {
                    <div class="text-center">
                        <img class="w-[54px] h-[42px] mb-[2px]" [src]="item.image" [alt]="item.label">
                        <div class="text-xs font-medium leading-5 text-[#34306A]">{{ item.label }}</div>
                    </div>
                }
            </div>
        </div>
    </div>
    <input #ngImageInput class="absolute invisible" type="file" (change)="fileChangeEvent($event)">
</div>

<app-modal-crop-image [imageChanged]="imageEvent" [ratio]="ratio" (cropSuccess)="handleCropDone($event)"/>
<app-modal-require-photo-id />
