import { Component } from '@angular/core';
import { CommonService } from '../../services/common.service';
import { ModalComponent } from '../modal/modal.component';

@Component({
    selector: 'app-modal-contact',
    standalone: true,
    imports: [
        ModalComponent
    ],
    templateUrl: './modal-contact.component.html',
    styleUrl: './modal-contact.component.scss'
})
export class ModalContactComponent {
    contacts = [
        {
            url: 'https://m.me/migiihsk',
            img: '/images/icons/ic_mess.svg',
            name: 'Messenger'
        },
        {
            url: 'https://wa.me/+84961285987',
            img: '/images/icons/ic_whatsapp_round.png',
            name: 'Whatsapp'
        },
        {
            url: 'https://zalo.me/0961285987',
            img: '/images/icons/ic_zalo.svg',
            name: '<PERSON><PERSON>'
        },
        {
            url: 'https://mail.google.com/mail/?view=cm&to=<EMAIL>',
            img: '/images/icons/ic_mail_round.png',
            name: 'Mail'
        }
    ];

    constructor(
        protected readonly commonService: CommonService
    ) {}

    ngOnInit() {}

}
