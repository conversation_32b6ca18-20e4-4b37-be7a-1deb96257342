app-modal {
    display: none;
}
app-modal .app-modal {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 99;
    overflow: auto;
}
app-modal .app-modal .app-modal-body {
    padding: 20px;
    margin: 40px auto;
}
app-modal .app-modal-background {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: #000;
    opacity: 0.75;
    z-index: 90;
}
body.app-modal-open {
    overflow: hidden;
}
.modal-dialog {
    margin: 10px auto;
}
.top-modal {
    z-index: 101 !important;
}
.top-background {
    z-index: 100 !important;
}
.modal-content .title {
    margin-bottom: 16px;
}
@media (min-width: 576px){
    .modal-sm {
        max-width: 300px;
    }
    .modal-md {
        max-width: 600px !important;
    }
}
@media(max-width: 576px){
    app-modal .app-modal .app-modal-body {
        padding: unset;
    }
}