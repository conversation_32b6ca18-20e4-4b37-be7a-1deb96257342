@let user = commonService.sUser();
<div *ngIf="!isExam" class="box-fixed">
    <header class="flex justify-between items-center bg-bg-white px-10 py-3">
        <div class="header-left flex gap-[45px]">
            <a [routerLink]="'/'" class="block min-w-[64px] logo-jopt">
                <img class="w-[64px] h-[40px]" src="/images/jopt/jopt.svg" alt="Logo Jopt">
            </a>
            <div class="header-nav flex items-center">
                <a [routerLink]="'/hsk-1/mock-test'" class="btn btn-lg"
                    [ngClass]="{' btn-solid btn-solid-light': !isActive('/hsk'), 'btn-on-color-dark': isActive('/hsk')}">
                    <span i18n="@@txt_home">Home</span>
                </a>
                <a routerLink="/upgrade" class="btn btn-lg"
                    [ngClass]="{'btn-solid btn-solid-light': !isActive('/upgrade'), 'btn-on-color-dark': isActive('/upgrade')}">
                    <span i18n="@@txt_upgrade">Upgrade</span>
                </a>
                <a href="https://migii.net" target="_blank" class="btn btn-lg btn-solid btn-solid-light">
                    <span i18n="@@txt_about_migii">About Migii</span>
                </a>
                <a [routerLink]="['/exam']" target="_blank" class="btn btn-lg btn-solid btn-solid-light">
                    <span i18n="@@txt_about_migii">Exam Register</span>
                </a>
            </div>
        </div>
        <div class="header-right">
            <div class="header-lang flex items-center gap-2">
                <a href="https://www.facebook.com/migiihsk/#" target="_blank"
                    class="btn btn-lg btn-solid btn-solid-light">
                    <span i18n="@@txt_support">Support</span>
                    <app-svg-icon name="question" fill="none" [width]="20" [height]="20"></app-svg-icon>
                </a>
                <div class="parent-hover">
                    <div class="flex gap-1 items-center font-action text-[14px] leading-[20px]  p-3">
                        <img class="w-6 h-6" src="/images/header/flag_{{language}}.png" alt="Flag">
                        <span>{{ selectedLang.name }}</span>
                        <app-svg-icon class="px-[4px] py-[5px]" name="arr_down" [fill]="'--ct-secondary'" [width]="16"
                            [height]="16"></app-svg-icon>
                    </div>
                    <div
                        class="child right-0 w-max user-menu top-full flex flex-col rounded-lg border-bg-secondary border-[1px] bg-bg-white">
                        @for (lang of languages; track lang; let i = $index) {
                        @if(i !== 0) {
                        <a [href]="language + currentUrl">
                            <div class="flex p-[6px] items-center gap-1">
                                <img class="w-6 h-6" src="/images/header/flag_{{lang.code}}.png" alt="Flag">
                                <div class="text-ct-primary">{{ lang.desc }}</div>
                            </div>
                        </a>
                        }
                        }
                    </div>
                </div>
                @if(user) {
                <div class="parent-hover flex items-center gap-2">
                    <img class="w-[40px] h-[40px] rounded-[50%]" [src]="'/images/header/avatar_default.png'"
                        alt="avatar">
                    <div class="flex gap-1 items-center">
                        <div class="flex flex-col gap-1">
                            <div class="standard-txt-14 font-[500]">{{ user.name }}</div>
                        </div>
                        <app-svg-icon class="px-[4px] py-[5px]" name="arr_down" [fill]="'--ct-secondary'" [width]="16"
                            [height]="16"></app-svg-icon>
                    </div>
                    <div
                        class="child w-max right-0 user-menu top-full flex flex-col rounded-lg border-bg-secondary border-[1px] bg-bg-white">
                        <div class="p-[6px]">
                            <div class="p-[6px] flex items-center gap-1" (click)="logout()">
                                <app-svg-icon name="logout" [fill]="'none'" [width]="16" [height]="18"></app-svg-icon>
                                <div class="standard-txt-14 text-ct-error-primary px-1" i18n="@@txt_logout">Đăng xuất
                                    tài khoản</div>
                            </div>
                        </div>
                    </div>
                </div>
                } @else {
                <button class="btn btn-lg btn-brand" (click)="openAuth()" i18n="@@txt_login">Đăng nhập</button>
                }
            </div>
        </div>
    </header>
</div>