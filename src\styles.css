/* You can add global styles to this file, and also import other style files */
@import "styles/_variables.css";
@import "styles/_light_theme.css";
@tailwind base;
@tailwind components;
@tailwind utilities;

@font-face {
    font-family: 'Inter';
    src: url('/fonts/Inter-VariableFont_opsz\,wght.ttf') format("ttf");
    font-weight: normal;
    font-style: normal;
}
@font-face {
    font-family: 'Noto Sans JP';
    src: url('/fonts/NotoSansJP-VariableFont_wght.ttf') format("ttf");
    font-weight: normal;
    font-style: normal;
}
@font-face {
    font-family: 'Noto Sans';
    src: url('/fonts/NotoSans-VariableFont_wdth\,wght.ttf') format("ttf");
    font-weight: normal;
    font-style: normal;
}
*, ::after, ::before {
    box-sizing: border-box;
}
body {
    background: var(--bg-primary); 
    color: var(--ct-primary);
    font-weight: 500;
    font-family: var(--font-body);
}
.pointer {
    cursor: pointer;
}
.pointer:hover {
    opacity: .8;
}
.box-body{
    width: 77%;
    margin: auto;
}
/* Loại bỏ màu nền và border khi autofill */
input:-webkit-autofill {
    -webkit-background-clip: text;
}

.loader {
    border: 5px solid #f3f3f3;
    border-top: 5px solid #3498db; /* Màu loader */
    border-radius: 50%;
    width: 50px;
    height: 50px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
        -webkit-transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
        -webkit-transform: rotate(360deg);
    }
}

.child{
    position: absolute;
    display: none;
}
.parent-hover {
    position: relative;
    cursor: pointer;
}
.parent-hover:hover .child {
    display: block;
}

.scrollbar-common::-webkit-scrollbar {
    width: 3px;
}
.scrollbar-common::-webkit-scrollbar-track {
    display: none;
}
.scrollbar-common::-webkit-scrollbar-thumb {
    background-color: #d9d9d9; 
    border-radius: 30px;
}
.scrollbar-common::-webkit-scrollbar-thumb:hover {
    background: gray;
}