<app-modal id="modal-login">
    <div class="fixed -translate-x-2/4 -translate-y-2/4 left-2/4 top-2/4 bg-white rounded-2xl max-w-[450px] w-[95%] py-5 pl-5 pr-2.5">
        <div class="max-h-[90vh] overflow-y-auto pr-2.5 scrollbar-common">
            <img class="w-[80px] h-[50px]" src="/images/jopt/jopt.svg" alt="JOPT">
            <div class="text-xl font-semibold leading-7 mt-3" i18n="@@txt_login">
                Login
            </div>
            <div class="text-ct-secondary text-sm leading-5 mt-1" i18n="@@txt_login_account_migiijlpt">
                You can log in with your registered account on the Migii JLPT app
            </div>
            <form class="flex flex-col gap-1" [formGroup]="dataLogin">
                <label for="email" class="text-ct-primary font-body standard-txt font-[400] mt-5">Email</label>
                <div class="input input-lg mt-1 flex items-center mt-1"
                    [class.input-err]="submitted && dataLogin.controls['email'].errors"
                    [class.input-success]="submitted && !dataLogin.controls['email'].errors">
                    <app-svg-icon name="email" [width]="18" [height]="16" fill="--fg-primary" ></app-svg-icon>
                    <input #firstInput formControlName="email" type="email" class="w-full outline-none bg-transparent" id="email" i18n-placeholder="@@txt_email_placeholder" placeholder="Enter your email">
                </div>
                <div class="flex items-center gap-1 mt-1">
                    @if(submitted && dataLogin.controls['email'].errors){
                        <app-svg-icon name="warning" fill="--ct-error-primary" [width]="16" [height]="16"></app-svg-icon>
                        @if(dataLogin.controls['email'].errors['email']){
                            <span class="text-[12px] text-ct-error-primary" i18n="@@txt_invalid_email">Invalid email</span>
                        }@if(dataLogin.controls['email'].errors['required']){
                            <span class="text-[12px] text-ct-error-primary" i18n="@@txt_field_blank">This field cannot be blank</span>
                        }
                    }
                </div>
    
                <label for="email" class="text-ct-primary font-body standard-txt font-[400]" i18n="@@txt_password">Password</label>
                <div class="input input-lg mt-1 flex item-center"
                    [class.input-err]="submitted && dataLogin.controls['password'].errors"
                    [class.input-success]="submitted && !dataLogin.controls['password'].errors">
                    <img class="w-4 h-4" src="/images/icons/ic_lock.svg" alt="Lock">
                    <input #firstInput formControlName="password" [type]="showPassword ? 'text' : 'password'" class="w-full outline-none bg-transparent" id="email" i18n-placeholder="@@txt_email_placeholder" placeholder="Enter your password">
                    <app-svg-icon class="pointer" name="eye_pass" [width]="18" [height]="9" (click)="toggleShowPass()"></app-svg-icon>
                </div>
                <div class="flex items-center gap-1 mt-1">
                    @if(submitted && dataLogin.controls['password'].errors){
                        <app-svg-icon name="warning" fill="--ct-error-primary" [width]="16" [height]="16"></app-svg-icon>
                        <span class="text-[12px] text-ct-error-primary" i18n="@@txt_field_blank">This field cannot be blank</span>
                    }
                </div>
            </form>
            @if(notiErr){
                <div class="flex items-center gap-1 mt-1">
                    <app-svg-icon name="warning" fill="--ct-error-primary" [width]="16" [height]="16"></app-svg-icon>
                    <span class="text-[12px] text-ct-error-primary">{{ notiErr }}</span>
                </div>
            } 
            <div class="text-sm text-medium leading-5 text-end" i18n="@@txt_forgot_pass">Forgot Password?</div>
    
            <div class="btn btn-lg btn-brand w-full text-medium mt-5" i18n="@@txt_login" (click)="loginWithEmail()">Login</div>
    
            <div class="flex items-center justify-center gap-2 my-4">
                <hr class="w-5 h-px bg-[#5D5A6F]" />
                <span class="text-sm leading-5 text-ct-secondary" i18n="@@txt_or_login_w">Or log in with</span>
                <hr class="w-5 h-px bg-[#5D5A6F]" />
            </div>
            <app-login-social #loginSocialComp />
            <div class="text-center mt-5">
                <span class="text-sm leading-5 text-ct-secondary" i18n="@@txt_dont_account">Don't have an account?</span>&nbsp;
                <span class="text-sm font-medium leading-5 text-ct-brand-secondary cursor-pointer" i18n="@@txt_signup_now" (click)="openAuth(1)">Sign up now</span>
            </div>
            <div class="p-[5px] flex items-center justify-center pointer absolute right-5 top-5 bg-[#0a0f290a] rounded-lg" (click)="commonService.closeModal('modal-login')">
                <img class="w-3.5 h-3.5" src="/images/icons/ic_close_modal.svg" alt="">
            </div>
        </div>
    </div>
</app-modal>