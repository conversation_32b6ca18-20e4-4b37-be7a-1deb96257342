import { AfterViewInit, Component, ElementRef, EventEmitter, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { isPlatformBrowser } from '@angular/common';
import { Inject, PLATFORM_ID } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { TutorialComponent } from '../tutorial/tutorial.component';
import { QuestionComponent } from '../question/question.component';
import { Output } from '@angular/core';

@Component({
  selector: 'app-do-exam',
  standalone: true,
  imports: [CommonModule, FormsModule, TutorialComponent, QuestionComponent],
  templateUrl: './do-exam.component.html',
  styleUrl: './do-exam.component.css'
})
export class DoExamComponent implements OnInit {
  showPopupTroble: boolean = false;
  isMic: boolean = false;

  ishaveTroble(isMic: boolean) {
    this.showPopupTroble = true;
    isMic == true ? this.isMic = true : this.isMic = false;

  }
  @Output() senDataCheckDevice = new EventEmitter<any>();

  @ViewChild('videoElement', { static: false }) videoElement!: ElementRef<HTMLVideoElement>;

  outputVolume: number = 30;
  inputVolume: number = 30;
  selectedOutputDevice: string = '';
  selectedInputDevice: string = '';
  isAudioTesting: boolean = false;
  isPlayingRecordedAudio: boolean = false;

  // Camera properties
  isCameraActive: boolean = false;
  cameraStream: MediaStream | null = null;
  cameraError: string | null = null;
  availableCameras: MediaDeviceInfo[] = [];
  selectedCamera: string = '';
  isCameraLoading: boolean = false;

  // Audio device properties
  availableOutputDevices: MediaDeviceInfo[] = [];
  availableInputDevices: MediaDeviceInfo[] = [];

  // Audio testing properties
  audioContext: AudioContext | null = null;
  analyser: AnalyserNode | null = null;
  microphone: MediaStreamAudioSourceNode | null = null;
  microphoneStream: MediaStream | null = null;
  dataArray: Uint8Array | null = null;
  animationId: number | null = null;
  audioVisualizationBars: number[] = Array(37).fill(0);
  recordedAudio: Blob | null = null;
  mediaRecorder: MediaRecorder | null = null;
  recordedChunks: Blob[] = [];
  data: any = {
    step: 2,
    outputVolume: this.outputVolume,
    inputVolume: this.inputVolume,
    selectedOutputDevice: this.selectedOutputDevice,
    selectedInputDevice: this.selectedInputDevice,
    selectedCamera: this.selectedCamera,
    // Thêm thông tin chi tiết thiết bị
    deviceInfo: {
      outputDeviceLabel: '',
      inputDeviceLabel: '',
      cameraLabel: ''
    }
  };


  constructor() {
    // this.loadAvailableDevices();
  }

  async ngOnInit() {
    await this.loadAvailableDevices();
    this.startCamera()
  }

  ngOnDestroy(): void {
    this.stopCamera();
    this.stopAudioTesting();
    if (this.audioContext) {
      this.audioContext.close();
    }
  }

  async loadAvailableDevices(): Promise<void> {
    try {
      // Request permissions first
      await navigator.mediaDevices.getUserMedia({ audio: true, video: true });

      const devices = await navigator.mediaDevices.enumerateDevices();
      this.availableCameras = devices.filter(device => device.kind === 'videoinput');
      this.availableOutputDevices = devices.filter(
        device => device.kind === 'audiooutput' && device.deviceId !== 'default' && device.deviceId !== 'communications'
      );
      this.availableInputDevices = devices.filter(
        device => device.kind === 'audioinput' && device.deviceId !== 'default' && device.deviceId !== 'communications'
      );

      // Set default selections
      if (this.availableCameras.length > 0) {
        this.selectedCamera = this.availableCameras[0].deviceId;
      }
      if (this.availableOutputDevices.length > 0) {
        this.selectedOutputDevice = this.availableOutputDevices[0].deviceId;
        this.data.selectedOutputDevice = this.selectedOutputDevice;
      }
      if (this.availableInputDevices.length > 0) {
        this.selectedInputDevice = this.availableInputDevices[0].deviceId;
        this.data.selectedInputDevice = this.selectedInputDevice;
      }
    } catch (error) {
      console.error('Error loading devices:', error);
      this.cameraError = 'Không thể tải danh sách thiết bị';
    }
  }

  async startCamera(): Promise<void> {
    try {
      this.isCameraLoading = true;
      this.cameraError = null;

      const constraints: MediaStreamConstraints = {
        video: {
          deviceId: this.selectedCamera ? { exact: this.selectedCamera } : undefined,
          width: { ideal: 640 },
          height: { ideal: 480 }
        },
        audio: false
      };

      this.cameraStream = await navigator.mediaDevices.getUserMedia(constraints);

      if (this.videoElement && this.videoElement.nativeElement) {
        this.videoElement.nativeElement.srcObject = this.cameraStream;
        this.videoElement.nativeElement.play();
      }

      this.isCameraActive = true;
    } catch (error: any) {
      console.error('Error starting camera:', error);
      this.handleCameraError(error);
    } finally {
      this.isCameraLoading = false;
    }
  }

  stopCamera(): void {
    if (this.cameraStream) {
      this.cameraStream.getTracks().forEach(track => track.stop());
      this.cameraStream = null;
    }

    if (this.videoElement && this.videoElement.nativeElement) {
      this.videoElement.nativeElement.srcObject = null;
    }

    this.isCameraActive = false;
  }

  toggleCamera(): void {
    if (this.isCameraActive) {
      this.stopCamera();
    } else {
      this.startCamera();
    }
  }

  async switchCamera(deviceId: any): Promise<void> {
    deviceId = deviceId.target.value || '';
    this.selectedCamera = deviceId;
    if (this.isCameraActive) {
      this.stopCamera();
      await this.startCamera();
    }
  }

  onOutputDeviceChange(deviceId: any): void {
    deviceId = deviceId.target.value || '';
    this.selectedOutputDevice = deviceId;
    this.data.selectedOutputDevice = deviceId;
  }

  onInputDeviceChange(deviceId: any): void {
    deviceId = deviceId.target.value || '';
    this.selectedInputDevice = deviceId;
    this.data.selectedInputDevice = deviceId;
  }

  onOutputVolumeChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.outputVolume = parseInt(target.value);
    this.data.outputVolume = this.outputVolume;
  }

  onInputVolumeChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.inputVolume = parseInt(target.value);
    this.data.inputVolume = this.inputVolume;
  }

  async startAudioTesting(): Promise<void> {
    try {
      this.isAudioTesting = true;
      this.recordedChunks = [];

      const constraints: MediaStreamConstraints = {
        audio: {
          deviceId: this.selectedInputDevice ? { exact: this.selectedInputDevice } : undefined,
          echoCancellation: true,
          noiseSuppression: true
          // Bỏ volume constraint vì không hoạt động
        }
      };

      this.microphoneStream = await navigator.mediaDevices.getUserMedia(constraints);

      // Setup audio context và gain node để control volume
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      this.analyser = this.audioContext.createAnalyser();
      this.analyser.fftSize = 256;

      const bufferLength = this.analyser.frequencyBinCount;
      this.dataArray = new Uint8Array(bufferLength);

      this.microphone = this.audioContext.createMediaStreamSource(this.microphoneStream);

      // Tạo gain node để control âm lượng mic
      const gainNode = this.audioContext.createGain();
      gainNode.gain.value = this.inputVolume / 100; // Áp dụng âm lượng mic

      this.microphone.connect(gainNode);
      gainNode.connect(this.analyser);

      // Setup media recorder với stream đã qua gain node
      const destination = this.audioContext.createMediaStreamDestination();
      gainNode.connect(destination);

      this.mediaRecorder = new MediaRecorder(destination.stream);
      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          this.recordedChunks.push(event.data);
        }
      };

      this.mediaRecorder.onstop = () => {
        this.recordedAudio = new Blob(this.recordedChunks, { type: 'audio/webm' });
        this.playRecordedAudio(); // Tự động phát lại ngay
      };

      this.mediaRecorder.start();
      this.visualizeAudio();

      // Stop recording after 3 seconds
      setTimeout(() => {
        this.stopAudioTesting();
      }, 3000);

    } catch (error) {
      console.error('Error starting audio test:', error);
      this.isAudioTesting = false;
    }
  }

  stopAudioTesting(): void {
    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {
      this.mediaRecorder.stop();
    }

    if (this.microphoneStream) {
      this.microphoneStream.getTracks().forEach(track => track.stop());
      this.microphoneStream = null;
    }

    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }

    this.audioVisualizationBars = Array(37).fill(0);
    this.isAudioTesting = false;
  }

  private visualizeAudio(): void {
    if (!this.analyser || !this.dataArray) return;

    this.analyser.getByteFrequencyData(this.dataArray);

    // Calculate average volume
    const average = this.dataArray.reduce((sum, value) => sum + value, 0) / this.dataArray.length;
    const normalizedVolume = Math.min(average / 128, 1); // Normalize to 0-1

    // Calculate how many bars should be active based on volume
    const activeBars = Math.floor(normalizedVolume * this.audioVisualizationBars.length);

    // Update visualization bars
    this.audioVisualizationBars = this.audioVisualizationBars.map((_, index) => {
      if (index < activeBars) {
        return Math.random() * 0.5 + 0.5; // Random height between 0.5 and 1
      }
      return 0;
    });

    if (this.isAudioTesting) {
      this.animationId = requestAnimationFrame(() => this.visualizeAudio());
    }
  }

  async playRecordedAudio(): Promise<void> {
    if (!this.recordedAudio) return;

    try {
      this.isPlayingRecordedAudio = true;

      const audioUrl = URL.createObjectURL(this.recordedAudio);
      const audio = new Audio(audioUrl);

      if ('setSinkId' in audio && this.selectedOutputDevice) {
        await (audio as any).setSinkId(this.selectedOutputDevice);
      }

      audio.volume = this.outputVolume / 100;

      audio.onended = () => {
        URL.revokeObjectURL(audioUrl);
        this.isPlayingRecordedAudio = false;
      };

      audio.onerror = () => {
        URL.revokeObjectURL(audioUrl);
        this.isPlayingRecordedAudio = false; // Reset on error
      };

      await audio.play();

    } catch (error) {
      console.error('Error playing recorded audio:', error);
      this.isPlayingRecordedAudio = false;
    }
  }

  getDeviceLabel(device: MediaDeviceInfo): string {
    return device.label || `${device.kind === 'audioinput' ? 'Microphone' : device.kind === 'audiooutput' ? 'Speaker' : 'Camera'} ${device.deviceId.substring(0, 8)}`;
  }

  private handleCameraError(error: any): void {
    if (error.name === 'NotAllowedError') {
      this.cameraError = 'Quyền truy cập camera bị từ chối. Vui lòng cho phép truy cập camera.';
    } else if (error.name === 'NotFoundError') {
      this.cameraError = 'Không tìm thấy camera nào.';
    } else if (error.name === 'NotReadableError') {
      this.cameraError = 'Camera đang được sử dụng bởi ứng dụng khác.';
    } else {
      this.cameraError = 'Lỗi không xác định khi truy cập camera.';
    }
  }

  startAudioTest(): void {
    if (this.isAudioTesting) {
      this.stopAudioTesting();
    } else {
      this.startAudioTesting();
    }
  }

  goBack(): void {
    // Implement navigation logic
    console.log('Going back...');
  }

  confirmDevices(): void {
    // Update device labels trước khi gửi
    const outputDevice = this.availableOutputDevices.find(d => d.deviceId === this.selectedOutputDevice);
    const inputDevice = this.availableInputDevices.find(d => d.deviceId === this.selectedInputDevice);
    const camera = this.availableCameras.find(d => d.deviceId === this.selectedCamera);

    this.data.deviceInfo = {
      outputDeviceLabel: outputDevice ? this.getDeviceLabel(outputDevice) : '',
      inputDeviceLabel: inputDevice ? this.getDeviceLabel(inputDevice) : '',
      cameraLabel: camera ? this.getDeviceLabel(camera) : ''
    };

    // Update latest values
    this.data.outputVolume = this.outputVolume;
    this.data.inputVolume = this.inputVolume;
    this.data.selectedOutputDevice = this.selectedOutputDevice;
    this.data.selectedInputDevice = this.selectedInputDevice;
    this.data.selectedCamera = this.selectedCamera;

    this.senDataCheckDevice.emit(this.data);
  }

  openHelp(): void {
    // Implement help functionality
    console.log('Opening help...');
  }

}


