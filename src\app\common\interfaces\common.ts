export interface ObjectKey<T> {
    [key: string | number]: T
}

export interface ResMessage<T> {
    message: string,
    total: number,
    data: T,
}

export interface AdsChild {
    name: string,
    package: string,
    link: string,
    button: string,
    image: string,
    action: string,
    description: string,
    title: string
}

export interface Ads {
    ad_id: number,
    ad_group_id: number,
    country: string,
    language: string,
    daily: number,
    popup_web: AdsChild[],
    top_1_list_web: AdsChild[],
    top_1_web: AdsChild[],
    top_2_web: AdsChild[],
    top_3_web: AdsChild[],
    top_4_web: AdsChild[],
    top_5_web: AdsChild[],
    top_6_web: AdsChild[],
    end_web: number,
    start_web: number,
    version_web: string,
    timeServer: number
}

export interface ResAdsinhouse {
    Err: string,
    status: number,
    Ads: Ads
}

export interface Language {
    desc: string;
    name: string;
    icon: string;
    height: number;
    code: string;
}

export interface SelectOption {
    value: any,
    label?: string,
    icon?: string
}