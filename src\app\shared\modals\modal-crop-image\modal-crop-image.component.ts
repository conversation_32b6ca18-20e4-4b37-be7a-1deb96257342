import { Component, EventEmitter, Input, Output } from '@angular/core';
import { ModalComponent } from "../../components/modal/modal.component";
import { ImageCroppedEvent, ImageCropperModule, LoadedImage } from 'ngx-image-cropper';
import { DomSanitizer, SafeUrl } from '@angular/platform-browser';
import { CommonService } from '../../services/common.service';
@Component({
    selector: 'app-modal-crop-image',
    standalone: true,
    imports: [
        ModalComponent,
        ImageCropperModule
    ],
    templateUrl: './modal-crop-image.component.html',
    styleUrl: './modal-crop-image.component.css'
})
export class ModalCropImageComponent {

    croppedImage: Blob | undefined;
    imageChangedEvent: any;
    @Input() ratio: number = 3/4;
    @Input() set imageChanged(data: any) {
        if(data) {
            this.imageChangedEvent = data;
            this.commonService.openModal('modal-crop-image');
        }
    };
    @Output() cancelUpdate = new EventEmitter();
    @Output() cropSuccess = new EventEmitter();

    constructor(
        private sanitizer: DomSanitizer,
        protected readonly commonService: CommonService
    ) {}

    imageCropped(event: ImageCroppedEvent) {
        if (event.blob) {
            this.croppedImage = event.blob;
        }
    }

    handleDone() {
        this.cropSuccess.emit(this.croppedImage);
        this.commonService.closeModal('modal-crop-image');
    }

    closeModal() {
        this.cancelUpdate.emit();
        this.commonService.closeModal('modal-crop-image');
    }
}
