export interface DataRegister {
    email: string,
    password: string,
    name: string,
    phone?: string
}

export interface User {
    id: number,
    name: string,
    email: string,
    day_of_birth: number,
    month_of_birth: number,
    year_of_birth: number,
    phone: string,
    access_token: string
}

export interface  JoptInfo {
    email: string,
    verify_email: number,
    is_completed: number,
    address: string,
    job: string,
    test_purpose: string,
    jopt_target: number,
    referral_source: string,
    has_taken_jlpt: boolean,
    total_years: number,
    total_months: number,
    number_cccd: string,
    image_cccd: string
}

export interface UserJoptInfo {
    user: User,
    jopt_info: JoptInfo
}

export interface DataInit {
    timeInitLogin: number,
    user: User
}