.device-test-container {
  width: 1440px;
  height: 1024px;
  margin-left: 16%;
  /* position: relative; */
  /* width: 100%; */
  /* min-height: 100vh; */
  background: var(--Background-bg-primary, #F9F9FB);
}


.page-content {
  margin-top: 6%;
  display: flex;
  width: 1120px;
  flex-direction: column;
  align-items: center;
  gap: var(--Spacing-spacing-32, 32px);
}


.content-container {
  display: flex;
  padding: var(--Spacing-spacing-0, 0px);
  justify-content: flex-end;
  align-items: center;
  gap: var(--Spacing-spacing-12, 12px);
  align-self: stretch;
}

.camera-section {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: flex-start;
  gap: 12px;
  flex: 1 0 0;
  align-self: stretch;
  border-radius: var(--Radius-radius-16, 16px);
  /* background: linear-gradient(298deg, rgba(0, 0, 0, 0.00) 70.17%, rgba(0, 0, 0, 0.20) 100%), url(<path-to-image>) lightgray 50% / cover no-repeat;
  background: linear-gradient(298deg, rgba(0, 0, 0, 0.00) 70.17%, rgba(0, 0, 0, 0.20) 100%), url(<path-to-image>) lightgray 50% / cover no-repeat; */
}

.camera-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: 100%;
  background-color: white;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.camera-video-container {
  position: relative;
  width: 100%;
  height: 450px;
  background-color: #1f2937;
  display: flex;
  align-items: center;
  justify-content: center;
}

.camera-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 0;
}

.camera-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #374151;
}

.placeholder-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.camera-placeholder-icon {
  opacity: 0.6;
}

.placeholder-text {
  color: #9ca3af;
  font-size: 0.875rem;
  font-weight: 500;
}

.camera-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(31, 41, 55, 0.9);
  gap: 1rem;
}

.loading-spinner {
  width: 2rem;
  height: 2rem;
  border: 3px solid #374151;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  color: white;
  font-size: 0.875rem;
  font-weight: 500;
}

.camera-error {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(239, 68, 68, 0.1);
  gap: 1rem;
  padding: 1rem;
  text-align: center;
}

.error-icon {
  font-size: 2rem;
}

.error-text {
  color: #dc2626;
  font-size: 0.875rem;
  font-weight: 500;
  max-width: 300px;
}

.camera-controls {
  width: 100%;
  border-top: 1px solid #e5e7eb;
}

.camera-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 1rem;
  background-color: white;
}

.camera-toggle-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.camera-select {
  display: flex;
  padding: var(-padding-none, 0px);
  flex-direction: column;
  align-items: flex-start;
  gap: var(--Spacing-spacing-4, 4px);
  align-self: stretch;
}

.camera-dropdown {
  padding: 0.375rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  background-color: white;
  font-size: 0.75rem;
  color: #374151;
  cursor: pointer;
  max-width: 150px;
}

.camera-dropdown:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 1px #3b82f6;
}

.camera-toggle-btn {
  display: flex;
  gap: 0.375rem;
  align-items: center;
  padding: 0.375rem 0.75rem;
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  font-weight: 500;
}

.camera-toggle-btn:hover:not(:disabled) {
  background-color: #f3f4f6;
  border-color: #d1d5db;
}

.camera-toggle-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.camera-toggle-btn .camera-text {
  color: #374151;
}

.hidden {
  display: none !important;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.alert-content {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
  align-items: center;
  flex: 1 0 0;
}

.alert-icon-container {
  display: flex;
  gap: 0.625rem;
  align-items: center;
  height: 1.25rem;
}

.alert-text {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  align-items: flex-start;
  flex: 1 0 0;
}

.alert-message {
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.25rem;
  color: #3b82f6;
}


.controls-section {
  display: flex;
  width: 400px;
  flex-direction: column;
  align-items: flex-start;
  gap: 20px;
}

.controls-container {
  /* display: flex;
  flex-direction: column;
  gap: 1.25rem;
  align-items: flex-start;
  width: 100%;
  height: 380px; */
}

.device-section {
  display: flex;
  padding: var(--Spacing-spacing-16, 16px);
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  gap: var(--Spacing-spacing-16, 16px);
  align-self: stretch;
  border-radius: var(--Radius-radius-16, 16px);
  background: var(--Background-bg-white, #FFF);
}

.device-section2 {
  display: flex;
  padding: var(--Spacing-spacing-16, 16px);
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  gap: var(--Spacing-spacing-16, 16px);
  align-self: stretch;
  border-radius: var(--Radius-radius-16, 16px);
  background: var(--Background-bg-white, #FFF);
}


.section-title {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  width: 100%;
}

.title-text {
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.25rem;
  flex: 1 0 0;
  color: #1f2937;
}

.device-dropdown {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  align-items: flex-start;
  width: 100%;
  padding: 0;
}

.dropdown-select {
  width: 100%;
  padding: 0.5rem;
  height: 2.5rem;
  background-color: white;
  border-radius: 0.5rem;
  border: 1px solid #f3f4f6;
  font-size: 0.875rem;
  line-height: 1.25rem;
  color: #1f2937;
  cursor: pointer;
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
}

.dropdown-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 1px #3b82f6;
}

.dropdown-select:disabled {
  background-color: #f9fafb;
  color: #9ca3af;
  cursor: not-allowed;
}

.volume-section {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  justify-content: center;
  align-items: flex-start;
  width: 100%;
}

.volume-label {
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.25rem;
  color: #1f2937;
}

.volume-control {
  display: flex;
  gap: 0.75rem;
  align-items: center;
  width: 100%;
}

.volume-slider-container {
  display: flex;
  gap: 0.75rem;
  align-items: center;
  flex: 1 0 0;
}

.volume-value {
  font-size: 0.875rem;
  line-height: 1.25rem;
  color: #9ca3af;
  min-width: 2rem;
  text-align: center;
}

.volume-slider-wrapper {
  flex: 1 0 0;
  position: relative;
}

.volume-slider {
  width: 100%;
  height: 0.375rem;
  border-radius: 999px;
  background-color: #f3f4f6;
  outline: none;
  appearance: none;
  cursor: pointer;
}

.volume-slider::-webkit-slider-thumb {
  appearance: none;
  width: 0.875rem;
  height: 0.875rem;
  border-radius: 50%;
  background-color: #14b8a6;
  cursor: pointer;
  box-shadow: 0px 0px 11px 0px rgba(128, 131, 141, 0.17);
}

.volume-slider::-moz-range-thumb {
  width: 0.875rem;
  height: 0.875rem;
  border-radius: 50%;
  background-color: #14b8a6;
  cursor: pointer;
  border: none;
  box-shadow: 0px 0px 11px 0px rgba(128, 131, 141, 0.17);
}

.volume-slider::-webkit-slider-track {
  height: 0.375rem;
  border-radius: 999px;
  background: linear-gradient(to right, #14b8a6 0%, #14b8a6 var(--value, 30%), #f3f4f6 var(--value, 30%), #f3f4f6 100%);
}

.volume-slider::-moz-range-track {
  height: 0.375rem;
  border-radius: 999px;
  background-color: #f3f4f6;
}

.volume-slider::-moz-range-progress {
  height: 0.375rem;
  border-radius: 999px;
  background-color: #14b8a6;
}

.audio-test-section {
  display: flex;
  padding: 16px;
  flex-direction: column;
  align-items: flex-start;
  gap: 16px;
  align-self: stretch;
  border-radius: 16px;
  background: var(--Background-bg-white, #FFF);
}

.test-info {
  display: flex;
  flex-direction: column;
  gap: 0.375rem;
  align-items: flex-start;
  width: 100%;
}

.test-title {
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.25rem;
  color: #1f2937;
}

.test-description {
  font-size: 0.75rem;
  font-weight: 500;
  line-height: 1.25rem;
  color: #6b7280;
  flex: 1 0 0;
}

.test-controls {
  display: flex;
  gap: 0.75rem;
  align-items: flex-end;
  width: 100%;
}

.test-button {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
  align-items: center;
  padding: 0.25rem 0.5rem;
  background-color: #3b82f6;
  border-radius: 0.5rem;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 100px;
}

.test-button:hover:not(:disabled) {
  background-color: #2563eb;
}

.test-button:disabled {
  background-color: #9ca3af;
  cursor: not-allowed;
}

.test-button .button-text {
  font-size: 0.75rem;
  font-weight: 500;
  line-height: 1.25rem;
  color: white;
}

.audio-visualizer {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  width: 100%;
  flex: 1 0 0;
  height: 2rem;
  gap: 1px;
}

.audio-bar {
  width: 0.375rem;
  min-height: 0.125rem;
  border-radius: 0.125rem;
  background-color: #d4d4d8;
  transition: all 0.1s ease;
  transform-origin: bottom;
}

.audio-bar.active {
  background-color: #1e40af;
  box-shadow: 0 0 4px rgba(30, 64, 175, 0.3);
}

.action-buttons {
  display: inline-flex;
  padding: var(--Spacing-spacing-0, 0px);
  justify-content: flex-end;
  align-items: center;
  gap: var(--Spacing-spacing-12, 12px);
  align-self: stretch;
}

.back-button {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
  align-items: center;
  padding: 0.75rem 0.5rem;
  height: 2.5rem;
  background-color: white;
  border-radius: 0.5rem;
  border: 1px solid #f3f4f6;
  width: 120px;
  cursor: pointer;
}

.back-button .button-text {
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.25rem;
  color: #1f2937;
}

.confirm-button {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
  align-items: center;
  padding: 0.75rem 0.5rem;
  height: 2.5rem;
  background-color: #14b8a6;
  border-radius: 0.5rem;
  border: none;
  cursor: pointer;
}

.confirm-button .button-text {
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.25rem;
  color: white;
}

.float-button {
  display: inline-flex;
  position: fixed;
  right: 1.5rem;
  bottom: 1.5rem;
  align-items: center;
  padding: 0.5rem;
  width: 4rem;
  height: 4rem;
  border-radius: 0.5rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  z-index: 1000;
  cursor: pointer;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header {
    display: flex;
    width: 1440px;
    padding: var(--Spacing-spacing-12, 12px) var(--Spacing-spacing-40, 40px);
    align-items: center;
    gap: var(--Spacing-spacing-40, 40px);
    background: var(--Background-White, #FFF);

    /* Shadow/01 */
    box-shadow: 0px 0px 11px 0px rgba(128, 131, 141, 0.17);
  }

  .controls-section {
    width: 100%;
  }

  .camera-toggle-container {
    flex-direction: column;
    gap: 0.5rem;
    align-items: stretch;
  }

  .camera-dropdown {
    max-width: none;
  }
}

@media (max-width: 640px) {
  .header {
    gap: 1rem;
    padding: 0.75rem 1rem;
  }

  .header-icons {
    gap: 0.75rem;
  }

  .logo-image {
    height: 2rem;
  }






  .camera-info {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .alert-content {
    justify-content: flex-start;
  }

  .action-buttons {
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 24px 0 0 0;
    position: relative;
    /* Đảm bảo không bị fixed nếu không muốn */
    margin-top: 32px;
    /* Đẩy xuống dưới cùng */
  }

  .action-buttons button {
    min-width: 160px;
  }
}

.text-btn-trobel {
  background: none;
  border: none;
  color: #007bff;
  cursor: pointer;
  font-size: 14px;
}

.popup-overlay-trobel {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.popup-box-trobel {
  background: white;
  width: 600px;
  max-height: 80vh;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.popup-header-trobel {
  padding: 16px;
  background: #f5f5f5;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #ddd;

  h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
  }
}

.close-btn-trobel {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
}

.popup-body-trobel {
  margin-left: 4%;
  padding: 16px;
  overflow-y: auto;

  img {
    max-width: 100%;
    border-radius: 8px;
    margin: 10px 0;
  }

  h4 {
    margin-top: 16px;
    font-size: 16px;
  }

  p {
    font-size: 14px;
    line-height: 1.5;
  }
}

.popup-footer-trobel {
  padding: 16px;
  border-top: 1px solid #ddd;
  display: flex;
  justify-content: flex-end;
  background: #f5f5f5;
}

.confirm-btn-trobel {
  width: 50%;
  background: #00b894;
  color: white;
  padding: 8px 16px;
  border-radius: 8px;
  border: none;
  cursor: pointer;
}

.text-btn-trobel {
  flex: 1 0 0;
  color: var(--Content-ct-secondary, #80838D);
  font-family: var(--Font-family-Body, Inter);
  font-size: var(--Font-size-md, 14px);
  font-style: normal;
  font-weight: var(--Font-weight-Regular, 400);
  line-height: var(--Font-line-height-sm, 20px);
  /* 142.857% */
}

.camera-view-trobel {
  margin-left: 4%;
  width: 516px;
  height: 344px;
  border-radius: 12px;
  border: 2px solid var(--Border-bd-primary, #E0E1E6);
}