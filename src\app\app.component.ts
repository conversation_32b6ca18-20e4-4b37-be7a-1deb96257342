import { Component, effect, ViewContainerRef } from '@angular/core';
import { Router, RouterOutlet } from '@angular/router';
import { HeaderComponent } from "./shared/components/header/header.component";
import { BroadcasterService } from './shared/services/broadcaster.service';
import { CommonService } from './shared/services/common.service';
import { NotifyComponent } from "./shared/components/notify/notify.component";
import { User, UserJoptInfo } from './common/interfaces/user';

@Component({
	selector: 'app-root',
	standalone: true,
	imports: [RouterOutlet, HeaderComponent, NotifyComponent],
	templateUrl: './app.component.html',
	styleUrl: './app.component.css'
})
export class AppComponent {

	timeTest: boolean = false;

	createdLogin: boolean = false;
	createdRegister: boolean = false;
	createdForgot: boolean = false;

	url: string = '';
	typeAuth: number = 0;
	user: User | undefined;

	constructor(
		private commonService: CommonService,
		private broadcaster: BroadcasterService,
		private viewContainerRef: ViewContainerRef,
		public router: Router
	) {
		effect(() => {
			if (this.commonService.sUser() && this.commonService.getEnvironment() === 'client') {
				this.commonService.getInfor().subscribe();
			}
		})
	}

	ngOnInit() {
		this.broadcaster.on<number>('auth').subscribe(async typeAuth => {
			this.typeAuth = typeAuth;
			switch (typeAuth) {
				case 1:
					if (!this.createdRegister) {
						import('./features/auth/register/register.component').then(m => {
							this.viewContainerRef.createComponent(m.RegisterComponent);
							this.createdRegister = true;
						});
					} else {
						this.openDynamicModal('modal-register');
					}
					break;
				default:
					if (!this.createdLogin) {
						import('./features/auth/login/login.component')
							.then(m => {
								this.viewContainerRef.createComponent(m.LoginComponent);
								this.createdLogin = true;
							});
					} else {
						this.openDynamicModal('modal-login');
					}
			}
		});
	}

	openDynamicModal(id: string) {
		this.commonService.closeAllModals();
		this.commonService.openModal(id);
	}

	handleUrls() {
		this.timeTest = this.url.includes('test/detail/');
	}
}
