import { Component, ViewChild } from '@angular/core';
import { ReactiveFormsModule, FormGroup, FormControl, Validators } from '@angular/forms';
import { ModalComponent } from '../../../shared/components/modal/modal.component';
import { CommonService } from '../../../shared/services/common.service';
import { SvgIconComponent } from "../../../shared/components/svg-icon/svg-icon.component";
import { LoginSocialComponent } from "../login-social/login-social.component";
import { UserService } from '../../../shared/services/user.service';
import { BroadcasterService } from '../../../shared/services/broadcaster.service';

@Component({
    selector: 'app-login',
    standalone: true,
    imports: [
        ModalComponent,
        ReactiveFormsModule,
        SvgIconComponent,
        LoginSocialComponent
    ],
    templateUrl: './login.component.html',
    styleUrls: [ 
        '../../../shared/styles/button.css',
		'../../../shared/styles/input.css',
        './login.component.css'
    ] 
})
export class LoginComponent {

    loading: boolean = false;
    submitted: boolean = false;
    showPassword: boolean = false;
    notiErr: string = '';
    dataLogin!: FormGroup;

    @ViewChild('loginSocialComp') loginSocialComp!: LoginSocialComponent;

    constructor(
        protected readonly commonService: CommonService,
        private userService: UserService,
        private broadcaster: BroadcasterService
    ) {}

    ngOnInit() {
        this.dataLogin = new FormGroup({
            email: new FormControl('', [Validators.required, Validators.email]),
			password: new FormControl('', [Validators.required])
        });
    }

    ngAfterViewInit() {
		this.commonService.closeAllModals();
        this.commonService.openModal('modal-login');
    }

    toggleShowPass() {
		this.showPassword = !this.showPassword;
	}

    loginWithEmail() {
		if( this.loading ) return;
		this.loading = true;
		this.submitted = true;

		if (this.dataLogin.invalid) {
			this.loading = false;
			return;
		}

		const inforLogin = this.dataLogin.value;
		this.loginSocialComp.loadDone = false;
		this.userService.login(inforLogin.email, inforLogin.password).subscribe({
            next: (res) => {
				this.loading = false;
                if(res.data) {
                    this.loginSocialComp.loginSuccess(res.data);
                }
			},
			error: (err) => {
				this.loading = false;
				this.loginSocialComp.loadDone = true;
				if(err.error.statusCode === 403) {
					this.notiErr = $localize `:@@txt_device_limited:Your account has exceeded the maximum number of login devices`;
				} else {
					this.notiErr = $localize `:@@txt_incorrect_login:Incorrect account or password`;
				}
				this.clearNotiError();
			}
        });
	}

    clearNotiError() {
		setTimeout(() => {
			this.notiErr = '';
		}, 5000);
	}

    openAuth(type: number) {
        this.broadcaster.broadcast('auth', type);
    }
}
