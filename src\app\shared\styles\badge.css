
/* @import '../../../styles/mixins.scss'; */
.badge{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    gap: 4px;
    font-size: 14px;
    line-height: 20px;
    font-weight: 500;
    padding: 0 6px;
    border-radius: 4px;
}
.badge-secondary{
    color: var(--ct-secondary);
    background: var(--bg-secondary);
}
.badge-brand{
    color: var(--ct-brand-primary);
    background: var(--bg-brand-primary);
}
.badge-warning{
    color: var(--ct-warning-primary);
    background: var(--bg-warning-primary);
}
.badge-info{
    color: var(--ct-info-primary);
    background: var(--bg-info-primary);
}
.badge-success{
    color: var(--ct-success-primary);
    background: var(--bg-success-primary);
}
.badge-error{
    color: var(--ct-error-primary);
    background: var(--bg-error-primary);
}
.badge-pre{
    background: linear-gradient(180deg, #FF9900 4.17%, #FFBA33 45.83%);
    color: var(--ct-white);
    border-radius: 8px;
}
.badge-hsk{
    background: linear-gradient(180deg, #21C99D 0%, #11BD9E 50%, #00B2A5 100%),linear-gradient(180deg, rgba(255, 255, 255, 0.16) 0%, rgba(255, 255, 255, 0) 100%);
    color: var(--ct-white);
    border-radius: 4px;
}
