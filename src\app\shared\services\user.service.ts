import { afterNextRender, Injectable } from '@angular/core';
import { Observable } from 'rxjs/internal/Observable';
import * as CONFIG from '../../common/constants/config';
import { HttpClient } from '@angular/common/http';
import { CommonService } from './common.service';
import { DataInit, DataRegister, User } from '../../common/interfaces/user';
import { ResMessage } from '../../common/interfaces/common';

@Injectable({
    providedIn: 'root'
})
export class UserService {

    loginStatus: boolean = false;
    timeInitLogin: number = 0;

    constructor(
        private http: HttpClient,
        private commonService: CommonService
    ) {
        afterNextRender(() => {
            const data = this.commonService.getLocalDataImp('data-init') as DataInit;
            if(data) {
                this.commonService.sUser.set(data.user ? data.user : undefined);
                this.timeInitLogin = data.timeInitLogin;
            }
        });
    }

    login(email: string, password: string) {
        const url = CONFIG.BASE_API_URL + 'v1/user/login';
        const data = {
            email,
            password
        }
        return this.http.post<ResMessage<User>>(url, data, CONFIG.HTTP_OPTION);
    }

    register(name: string, email: string, password: string, phone?: string) {

        const url = CONFIG.BASE_API_URL + 'v1/user/register';
        let data: DataRegister = {
            name,
            email,
            password
        }
        if(phone && phone.trim()) {
            data.phone = phone
        }
        return this.http.post<User>(url, data, CONFIG.HTTP_OPTION);
    }

    initLogin() {
        const url = CONFIG.BASE_API_URL + 'v1/user/init-login';
        let oldUser = this.commonService.sUser();
        if(this.timeInitLogin > 0 && oldUser) {
            if(this.timeInitLogin < Date.now() / 1000) {
                this.http.post<ResMessage<User>>(url, undefined, CONFIG.HTTP_OPTION).subscribe({
                    next: (res) => {
                        this.setInforUser({...oldUser, ...res.data});
                    },
                    error: (err) => {
                        this.clearInforUser();
                    },
                });
            }
        } else {
            this.clearInforUser();
        }
    }

    callInitLogin() {
        const url = CONFIG.BASE_API_URL + 'v1/user/init-login';
        return this.http.post<User>(url, undefined, CONFIG.HTTP_OPTION);
    }

    logout() {
        const url = CONFIG.BASE_API_URL + 'v1/user/logout';
        return this.http.post(url, undefined, CONFIG.HTTP_OPTION);
    }

    setInforUser(user: User) {
        this.commonService.sUser.set(user);
        const time = new Date();
        time.setHours(0, 0, 0, 0);
        const data: DataInit = {
            timeInitLogin: time.getTime() / 1000 + 86400,
            user
        }
        this.timeInitLogin = data.timeInitLogin;
        this.commonService.setLocalDataImp('data-init', data);
        this.commonService.sUser.set(user);
    }

    clearInforUser() {
        this.commonService.setLocalDataImp('data-init', {timeInitLogin: 0});
        this.commonService.sUser.set(undefined);
    }

    getInforUser() {
        return this.commonService.sUser();
    }

    setLoginStatus(login: boolean) {
        this.loginStatus = login;
    }

    loginWithGoogle(name: string, token: string) {
        const urlLogin = CONFIG.BASE_API_URL + 'v1/user/login-google';
        const dataSocial = {
            token,
            name,
            language: this.commonService.langBE
        }
        return this.http.post<ResMessage<User>>(urlLogin, dataSocial, CONFIG.HTTP_OPTION);
    }


    loginWithApple(name: string, token: string) {
        const urlLogin = CONFIG.BASE_API_URL + 'v1/user/login-apple';
        const dataSocial = {
            access_token: token,
            name,
            language: this.commonService.langBE
        }
        return this.http.post<ResMessage<User>>(urlLogin, dataSocial, CONFIG.HTTP_OPTION);
    }

    sendEmailForgotPass(email: string) {
        const url = CONFIG.BASE_API_URL + 'v1/user/sendMail';
        return this.http.post(url, { email }, CONFIG.HTTP_OPTION);
    }

    resetPassword(email: string, code: string, new_password: string) {
        const url = CONFIG.BASE_API_URL + 'v1/user/resetPassword';
        const dataSend = {
            email,
            code,
            new_password
        }
        return this.http.post(url, dataSend, CONFIG.HTTP_OPTION);
    }

    verifyEmail(lang: string) {
        const url = CONFIG.BASE_API_URL + 'v1/user/verify-email?language=' + lang;
        return this.http.get(url, CONFIG.HTTP_OPTION);
    }

    verifyTokenEmail(token: string) {
        const url = CONFIG.BASE_API_URL + 'v1/user/verify-email';
        return this.http.post(url, { token });
    }
}
