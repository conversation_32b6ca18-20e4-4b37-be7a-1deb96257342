
<div class="md:grid md:grid-cols-3 container mx-auto mt-4 mb-5 gap-4">
    <div class="md:col-span-2">
        <div class="p-4 rounded-2xl bg-bg-white">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-lg m-0" i18n="@@txt_pay_by_transf">Payment by bank transfer</h2>
                <div class="no-underline text-ct-info-secondary text-sm leading-5 cursor-pointer" i18n="@@txt_change_pay" (click)="changePayMethod()">Change pay method</div>
            </div>
            <div class="bg-bg-primary p-4 rounded-2xl xl:flex">
                @if(langPriceNew === 'vi' || langPriceNew === 'zh' || langPriceNew === 'zh-Hant') {
                    <div class="bg-bg-brand-pressed md:w-6/12 p-4 rounded-2xl mb-4 mx-auto md:mb-0 md:mx-auto max-w-[275px]">
                        <img class="w-full aspect-[1/1] object-contain" [src]=" langPriceNew === 'vi' && qRCode ? qRCode.qrcode_url : '/images/Alipay_QR_1.jpg'" alt="">
                        <div class="bg-bg-white text-xs leading-5 p-2 rounded-lg mt-4 flex items-center justify-center cursor-pointer" (click)="downloadQr(langPriceNew === 'vi' && qRCode ? qRCode.qrcode_url : '/images/jopt/Alipay_QR_1.jpg')">
                            <img class="mr-1" src="/images/icons/download.svg" alt="Download QR">
                            <span i18n="@@txt_download_qr">Download QR code</span>
                        </div>
                    </div>
                }
                
                <div class="w-full flex flex-col justify-between {{ langPriceNew === 'vi' || langPriceNew === 'zh' || langPriceNew === 'zh-Hant' ? 'xl:pl-10 pl-0' : '' }}">
                    <div>
                        @if(packDetail) {
                            @for (bank of countryBank[langPriceNew]; track $index) {
                                <ng-container [ngTemplateOutlet]="ngBankInfor" 
                                    [ngTemplateOutletContext]="{
                                        bank,
                                        content: langPriceNew === 'vi' && qRCode ? qRCode.transaction_code : '',
                                        amount: ((packDetail.sale ? packDetail.sale.prices[langPriceNew] : packDetail.prices[langPriceNew].price) * 0.95 | roundPrice:packDetail.prices[langPriceNew].currency) + packDetail.prices[langPriceNew].symbol,
                                    }"
                                >
                                </ng-container>
                                @if($index < countryBank[langPriceNew].length - 1) {
                                    <div class="my-6" i18n="@@txt_or">Or</div>
                                }
                            }
                        }
                    </div>
                    @if(langPriceNew === 'vi') {
                        <div class="bg-[#FF5C000E] pt-2 pb-3 px-0 rounded-lg">
                            <div class="text-center text-sm text-ct-secondary" i18n="@@txt_transac_end_af">Giao dịch kết thúc sau</div>
                            <div class="flex items-center justify-center mt-2">
                                <div class="leading-5 text-ct-white rounded bg-bg-warning-solid font-medium px-1 py-0.5">03</div>
                                &nbsp;:&nbsp;
                                <div class="leading-5 text-ct-white rounded bg-bg-warning-solid font-medium px-1 py-0.5">00</div>
                            </div>
                        </div>
                    }
                </div>

            </div>
        </div>

        <div class="p-4 rounded-2xl bg-bg-white mt-4">
            <h2 class="text-lg m-0 mb-4" i18n="@@txt_qr_pay_guide">Instructions for scanning QR code for payment</h2>
            @if(langPriceNew === 'vi') {
                <div class="flex items-start text-sm leading-5 mb-4">
                    <div class="min-w-[20px] h-5 bg-bg-info-solid text-ct-white rounded-[50%] flex items-center justify-center mr-2">2</div>
                    <span i18n="@@txt_step_bank_2">Open <span class="font-semibold">QR Code-enabled banking app</span> on your phone</span>
                </div>
                <div class="flex items-start text-sm leading-5 mb-4">
                    <div class="min-w-[20px] h-5 bg-bg-info-solid text-ct-white rounded-[50%] flex items-center justify-center mr-2">3</div>
                    <span i18n="@@txt_step_bank_3" class="flex items-center">On the application, select the feature&nbsp;<img src="/images/icons/ic_scan.svg" alt="Scan">&nbsp;<span class="fw-bold">Scan QR Code</span></span>
                </div>
                <div class="flex items-start text-sm leading-5 mb-4">
                    <div class="min-w-[20px] h-5 bg-bg-info-solid text-ct-white rounded-[50%] flex items-center justify-center mr-2">4</div>
                    <span i18n="@@txt_step_bank_4">Scan the QR code on this page and pay</span>
                </div>
                <div class="flex items-start text-sm leading-5 mb-4">
                    <div class="min-w-[20px] h-5 bg-bg-info-solid text-ct-white rounded-[50%] flex items-center justify-center mr-2">5</div>
                    <span i18n="@@txt_step_bank_5">Check account information, amount, transfer content</span>
                </div>
                <div class="flex items-start text-sm leading-5 mb-4">
                    <div class="min-w-[20px] h-5 bg-bg-info-solid text-ct-white rounded-[50%] flex items-center justify-center mr-2">6</div>
                    <span i18n="@@txt_step_bank_6">Make a transaction</span>
                </div>
                <div class="flex items-start text-sm leading-5">
                    <div class="min-w-[20px] h-5 bg-bg-info-solid text-ct-white rounded-[50%] flex items-center justify-center mr-2">7</div>
                    <span i18n="@@txt_step_bank_7">Click "Confirm Payment" to resync the upgraded account</span>
                </div>
            } @else {
                @for (item of stepBankOther; track $index) {
                    <div class="flex items-start text-sm leading-5 mb-4">
                        <div class="min-w-[20px] h-5 bg-bg-info-solid text-ct-white rounded-[50%] flex items-center justify-center mr-2">{{ $index + 1 }}</div>
                        <span>{{ item }}</span>
                    </div>
                }
                <div class="text-sm leading-5 mb-3 ps-3">
                    <div class="mb-2 font-semibold" i18n="@@txt_bank_2_1">
                        After you transfer the payment, please send me a screenshot of the transfer invoice and tell me the email 
                        address you used to create the account in the app
                    </div>
                </div>
            }

            <div class="text-center flex items-center mt-4">
                <span class="mr-2" i18n="@@txt_you_h_ques">If you have any questions during the payment process please contact:</span>
                <a target="_blank" href="https://m.me/migiihsk">
                    <img width="28" class="mr-2" src="/images/icons/ic_mess.svg" alt="Messages">
                </a>
                <a href="https://wa.me/+***********" target="_blank">
                    <img width="28" class="mr-2" src="/images/icons/ic_whatsapp_round.png" alt="Whatsapp">
                </a>
                    <a href="https://zalo.me/**********" target="_blank">
                    <img width="28" class="mr-2" src="/images/icons/ic_zalo.svg" alt="Zalo">
                </a>
                <a href="https://mail.google.com/mail/?view=cm&to=<EMAIL>" target="_blank">
                    <img width="28" class="mr-2 rounded-[50%] border-[0.1px] border-solid border-bg-info-solid" src="/images/icons/ic_mail_round.png" alt="Mail">
                </a>
            </div>
        </div>
    </div>
    <div class="md:col-span-1">
        <div class="p-4 rounded-2xl bg-bg-white">
            <app-user-card />
        </div>
        <div class="p-4 rounded-2xl bg-bg-white info-pack mt-3">
            <h2 class="text-lg m-0 mb-3" i18n="@@txt_order_info">Order information</h2>
            @if(packDetail) {
                <app-order 
                    [packDetail]="packDetail" 
                    [total]="(packDetail.sale ? packDetail.sale.prices[langPriceNew] : packDetail.prices[langPriceNew].price) * 0.95 | roundPrice:packDetail.prices[langPriceNew].currency"
                    [lang]="langPriceNew"
                />
            }
        </div>

        @if(packDetail) {
            <div class="btn-brand text-center text-sm mt-5 px-2 py-3 rounded-lg cursor-pointer" i18n="@@txt_pay_confirmed_contact" (click)="commonService.openModal('modal-contact-migii')">
                Confirm Payment
            </div>
        }
    </div>
</div>

<ng-template #ngBankInfor let-bank="bank" let-content="content" let-amount="amount">
    <div class="mb-3 flex items-center justify-between">
        <span class="font-medium text-sm" i18n="@@txt_bank_name">Bank name</span>
        @if(bank.img) {
            <img [src]="'/images/icons/' + bank.img" alt="ACB">
        } @else {
            <span class="font-semibold">{{ bank.bankName }}</span>
        }
    </div>
    <div class="mb-3 flex items-center justify-between">
        <span class="font-medium text-sm" i18n="@@txt_account_name">Account name</span>
        <span class="text-ct-secondary text-sm leading-5">{{ bank.accountName }}</span>
    </div>
    <div class="mb-3 flex items-center justify-between">
        <span class="font-medium text-sm" i18n="@@txt_account_number">Account number</span>
        <div class="flex items-center">
            <span class="text-ct-secondary text-sm leading-5">{{ bank.accountNumber }}</span>
            <div class="bg-bg-white cursor-pointer text-xs leading-5 font-medium px-2 py-1 rounded-lg flex items-center ml-2" 
            (click)="copyText(bank.accountNumber)">
                <img src="/images/icons/copy-01.svg" alt="Copy">
                <span class="ml-1" i18n="@@txt_copy">Copy</span>
            </div>
        </div>
    </div>
    @if(content) {
        <div class="mb-3 flex items-center justify-between">
            <span class="font-medium text-sm" i18n="@@txt_content">Content</span>
            <div class="flex items-center">
                <span class="text-ct-secondary text-sm leading-5">{{ content }}</span>
                <div class="bg-bg-white cursor-pointer text-xs leading-5 font-medium px-2 py-1 rounded-lg flex items-center ml-2" 
                (click)="copyText(content)">
                    <img src="/images/icons/copy-01.svg" alt="Copy">
                    <span class="ml-1" i18n="@@txt_copy">Copy</span>
                </div>
            </div>
        </div>
    }
    <div class="mb-3 flex items-center justify-between">
        <span class="font-medium text-sm" i18n="@@txt_amount_transfer">Amount to transfer</span>
        <span class="font-medium">{{ amount }}</span>
    </div>
</ng-template>

<app-modal-contact>
    <div class="text-center text-lg leading-7 mb-1" i18n="@@txt_contact_to_channel">You can contact Migii to confirm payment via the channels below</div>
    <div class="text-center text-sm leading-5 text-ct-secondary" i18n="@@txt_take_photo_receipt">Don't forget to take a photo of the receipt and send it to Migii</div>
</app-modal-contact>