import { Component, ElementRef, ViewChild } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SvgIconComponent } from "../../../shared/components/svg-icon/svg-icon.component";
import { CommonService } from '../../../shared/services/common.service';
import { PaymentService } from '../../../shared/services/payment.service';
import { examJoptFee, LIST_COUNTRY } from '../../../common/constants/schedule-pay';
import { RoundPricePipe } from "../../../common/pipes/roundPrice.pipe";
import { UserCardComponent } from "./user-card/user-card.component";
import { SaleItem, SalePackage } from '../../../common/interfaces/payment';
import { RegiterExamService } from '../../../shared/services/regiter-exam.service';
import { RoundExam } from '../../../common/interfaces/regiter-exam';
import { Router } from '@angular/router';
import { ModalRegisterSuccessComponent } from "./modal-register-success/modal-register-success.component";

declare var paypal: any;

@Component({
    selector: 'app-schedule-pay',
    standalone: true,
    imports: [FormsModule, SvgIconComponent, RoundPricePipe, UserCardComponent, ModalRegisterSuccessComponent],
    templateUrl: './schedule-pay.component.html',
    styleUrls: [
        '../../../shared/styles/button.css',
        '../../../shared/styles/input.css',
        './schedule-pay.component.css'
    ]
})
export class SchedulePayComponent {

    loadDone: boolean = false;
    submitted: boolean = false;

    langWeb: string = '';
    langPrice: string = 'en';
    countryCode: string = '';
    langPriceNew: string = 'en';
    keyPack: string = examJoptFee.id;

    bankForeign: boolean = false;
    method: number = 0;
    idxRoundExam: number = -1;
    idxSlotExam: number = -1;
    // 1. chuyển khoản qua ngân hàng
    // 0. thanh toán bằng paypal

    packDetail: SalePackage<SaleItem | undefined> | undefined = examJoptFee;
    @ViewChild('paypal') paypalElement!: ElementRef;

    listCountry = LIST_COUNTRY;
    roundExams: RoundExam[] = [];

    constructor(
        protected readonly commonService: CommonService,
        private paymentService: PaymentService,
        private regiterExamService: RegiterExamService,
        private router: Router
    ) { }

    ngOnInit() {
        this.langWeb = this.commonService.lang;
        this.regiterExamService.getSchedule().subscribe({
            next: res => {
                this.roundExams = res.data;
            }
        })
        this.paymentService.initpaymentMethod();
    }

    ngAfterViewInit() {
        this.commonService.scrollToTop();
        const loadSubcription = this.paymentService.loadPayLibrary.subscribe(res => {
            if(res === 'paypal') {
                this.createPaypal();
                loadSubcription.unsubscribe();
            }
        });
    }

    changeMethod(method: number, isForeign: boolean) {
        this.bankForeign = isForeign;
        this.method = method;
        this.langPriceNew = this.langPrice;
        this.paymentService.langPriceNew = this.langPriceNew;
    }

    createPaypal() {
        const self = this;
        if ((typeof paypal !== 'undefined') && this.packDetail && this.keyPack) {
            paypal.Buttons({
                createOrder: (data: any, actions: any) => {
                    if(!self.paymentService.roudExamId || !self.paymentService.slotExamId) {
                        self.submitted = true;
                        self.commonService.scrollToTop();
                        return;
                    } else {
                        const dataReference = { 
                            price: self.packDetail!.prices[this.langPriceNew], 
                            product_id_sale: self.packDetail!.productId, 
                            slot_id: self.paymentService.slotExamId 
                        };
                        const referenceId = btoa(unescape(encodeURIComponent(JSON.stringify(dataReference))));
    
                        self.loadDone = false;
                        const sell_price = self.packDetail!.sale ? self.packDetail!.sale.prices['en'] : self.packDetail!.prices['en'].price;
                        return actions.order.create({
                            purchase_units: [{
                                reference_id: referenceId,
                                description: self.keyPack,
                                amount: {
                                    value: sell_price,
                                    currency_code: 'USD',
                                    breakdown: {
                                        item_total: {
                                            currency_code: "USD",
                                            value: sell_price
                                        }
                                    }
                                },
                                items: [
                                    {
                                        name: self.keyPack,
                                        description: self.keyPack,
                                        unit_amount: {
                                            currency_code: "USD",
                                            value: sell_price
                                        },
                                        quantity: "1"
                                    }
                                ]
                            }]
                        });
                    }
                },
                onApprove: (data: any, actions: any) => {
                    return actions.order.capture().then((details: any) => {
                        self.commonService.openModal('modal-register-exam-success');
                    });
                },
                onCancel: (data: any) => {
                    self.loadDone = true;
                    // Show a cancel page, or return to cart 
                },
                onError: (err: any) => {
                    console.log(err);
                    self.loadDone = true;
                    // Show an error page here, when an error occurs
                }

            }).render(this.paypalElement.nativeElement);
        }
    }

    ngOnChangeRound() {
        this.paymentService.roudExamId = this.roundExams[this.idxRoundExam].id;
    }

    ngOnChangeSlot() {
        this.paymentService.slotExamId = this.roundExams[this.idxRoundExam].slots[this.idxSlotExam].id;
    }

    payBank() {
        this.submitted = true;
        if(this.paymentService.roudExamId && this.paymentService.slotExamId) {
            this.router.navigate(['/schedule/pay']);
        } else {
            this.commonService.scrollToTop();
        }
    }
}
