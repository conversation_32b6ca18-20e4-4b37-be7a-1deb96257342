<app-modal id="modal-register">
    <div class="fixed -translate-x-2/4 -translate-y-2/4 left-2/4 top-2/4 bg-white rounded-2xl max-w-[450px] w-[95%] py-5 pl-5 pr-2.5">
        <div class="max-h-[90vh] overflow-y-auto pr-2.5 scrollbar-common">
            <img class="w-[80px] h-[50px]" src="/images/jopt/jopt.svg" alt="JOPT">
            <div class="text-xl font-semibold leading-7 mt-3" i18n="@@txt_signup">
                Sign up
            </div>
            <div class="text-ct-secondary text-sm leading-5 mt-1" i18n="@@txt_login_account_migiijlpt">
                You can log in with your registered account on the Migii JLPT app
            </div>
            <form class="flex flex-col gap-1" [formGroup]="dataSignup">

                <label for="name" class="font-body standard-txt mt-5 flex items-center gap-1">
                    <span class="text-ct-primary text-sm" i18n="@@txt_full_name">Full name</span>
                    <span class="text-lg text-ct-error-primary">*</span>
                </label>
                <div class="input input-lg mt-1 flex items-center"
                    [class.input-err]="submitted && dataSignup.controls['name'].errors"
                    [class.input-success]="submitted && !dataSignup.controls['name'].errors">
                    <app-svg-icon name="email" [width]="18" [height]="16" fill="--fg-primary" ></app-svg-icon>
                    <input #firstInput formControlName="name" type="name" class="w-full outline-none bg-transparent" id="name" i18n-placeholder="@@txt_enter_name" placeholder="Enter your name">
                </div>
                <div class="flex items-center gap-1 mt-1">
                    @if(submitted && dataSignup.controls['name'].errors){
                        <app-svg-icon name="warning" fill="--ct-error-primary" [width]="16" [height]="16"></app-svg-icon>
                        @if (dataSignup.controls['name'].errors['required']) {
                            <span class="text-[12px] text-ct-error-primary" i18n="@@txt_field_blank">This field cannot be blank</span>
                        }@else if (dataSignup.controls['name'].errors['minLength']) {
                            <span class="text-[12px] text-ct-error-primary" i18n="@@txt_err_name_min">Name must be at least 2 characters</span>
                        }@else if (dataSignup.controls['name'].errors['required']) {
                            <span class="text-[12px] text-ct-error-primary" i18n="@@txt_err_name_max">Name no more than 100 characters</span>
                        }
                    }
                </div>

                <label for="name" class="font-body standard-txt flex items-center gap-1">
                    <span class="text-ct-primary text-sm">Email</span>
                    <span class="text-lg text-ct-error-primary">*</span>
                </label>
                <div class="input input-lg flex items-center"
                    [class.input-err]="submitted && dataSignup.controls['email'].errors"
                    [class.input-success]="submitted && !dataSignup.controls['email'].errors">
                    <app-svg-icon name="email" [width]="18" [height]="16" fill="--fg-primary" ></app-svg-icon>
                    <input #firstInput formControlName="email" type="email" class="w-full outline-none bg-transparent" id="email" i18n-placeholder="@@txt_email_placeholder" placeholder="Enter your email">
                </div>
                <div class="flex items-center gap-1 mt-1">
                    @if(submitted && dataSignup.controls['email'].errors){
                        <app-svg-icon name="warning" fill="--ct-error-primary" [width]="16" [height]="16"></app-svg-icon>
                        @if(dataSignup.controls['email'].errors['email']){
                            <span class="text-[12px] text-ct-error-primary" i18n="@@txt_invalid_email">Invalid email</span>
                        }@else if(dataSignup.controls['email'].errors['required']){
                            <span class="text-[12px] text-ct-error-primary" i18n="@@txt_field_blank">This field cannot be blank</span>
                        }
                    }
                </div>
    
                <label for="password" class="font-body standard-txt flex items-center gap-1">
                    <span class="text-ct-primary text-sm" i18n="@@txt_password">Password</span>
                    <span class="text-lg text-ct-error-primary">*</span>
                </label>
                <div class="input input-lg flex item-center"
                    [class.input-err]="submitted && dataSignup.controls['password'].errors"
                    [class.input-success]="submitted && !dataSignup.controls['password'].errors">
                    <img class="w-4 h-4" src="/images/icons/ic_lock.svg" alt="Lock">
                    <input #firstInput formControlName="password" [type]="showPassword ? 'text' : 'password'" class="w-full outline-none bg-transparent" id="password" i18n-placeholder="@@txt_enter_pass" placeholder="Enter your password">
                    <app-svg-icon class="pointer" name="eye_pass" [width]="18" [height]="9" (click)="showPassword = !showPassword"></app-svg-icon>
                </div>
                <div class="flex items-center gap-1 mt-1">
                    @if(submitted && dataSignup.controls['password'].errors){
                        <app-svg-icon name="warning" fill="--ct-error-primary" [width]="16" [height]="16"></app-svg-icon>
                        @if (dataSignup.controls['password'].errors['required']) {
                            <span class="text-[12px] text-ct-error-primary" i18n="@@txt_field_blank">This field cannot be blank</span>
                        }
                        @else if (dataSignup.controls['password'].errors['minlength']) {
                            <span class="text-[12px] text-ct-error-primary" i18n="@@txt_pw_length">Password must be at least 6 characters long</span>
                        }
                    }
                </div>

                <label for="re-password" class="font-body standard-txt flex items-center gap-1">
                    <span class="text-ct-primary text-sm" i18n="@@txt_cf_password">Confirm password</span>
                    <span class="text-lg text-ct-error-primary">*</span>
                </label>
                <div class="input input-lg flex item-center"
                    [class.input-err]="submitted && (dataSignup.controls['rePassword'].errors || dataSignup.value.password !== dataSignup.value.rePassword)"
                    [class.input-success]="submitted && !dataSignup.controls['rePassword'].errors">
                    <img class="w-4 h-4" src="/images/icons/ic_lock.svg" alt="Lock">
                    <input #firstInput formControlName="rePassword" [type]="showRePassword ? 'text' : 'password'" class="w-full outline-none bg-transparent" id="re-password" i18n-placeholder="@@txt_confirm_pass" placeholder="Confirm your password">
                    <app-svg-icon class="pointer" name="eye_pass" [width]="18" [height]="9" (click)="showRePassword = !showRePassword"></app-svg-icon>
                </div>
                <div class="flex items-center gap-1 mt-1">
                    @if (submitted) {
                        @if(dataSignup.controls['rePassword'].errors) {
                            <app-svg-icon name="warning" fill="--ct-error-primary" [width]="16" [height]="16"></app-svg-icon>
                            @if (dataSignup.controls['rePassword'].errors['required']) {
                                <span class="text-[12px] text-ct-error-primary" i18n="@@txt_field_blank">This field cannot be blank</span>
                            }
                            @else if (dataSignup.controls['rePassword'].errors['minlength']) {
                                <span class="text-[12px] text-ct-error-primary" i18n="@@txt_pw_length">Password must be at least 6 characters long</span>
                            }
                        } @else if (dataSignup.value.password !== dataSignup.value.rePassword) {
                            <app-svg-icon name="warning" fill="--ct-error-primary" [width]="16" [height]="16"></app-svg-icon>
                            <span class="text-[12px] text-ct-error-primary" i18n="@@txt_pass_match">Password is not match</span>
                        }
                    }
                </div>
            </form>
            @if(notiErr){
                <div class="flex items-center gap-1 mt-1">
                    <app-svg-icon name="warning" fill="--ct-error-primary" [width]="16" [height]="16"></app-svg-icon>
                    <span class="text-[12px] text-ct-error-primary">{{ notiErr }}</span>
                </div>
            }
    
            <div class="btn btn-lg btn-brand w-full text-medium mt-5" i18n="@@txt_login">Sign Up</div>
    
            <div class="flex items-center justify-center gap-2 my-4">
                <hr class="w-5 h-px bg-[#5D5A6F]" />
                <span class="text-sm leading-5 text-ct-secondary" i18n="@@txt_or_login_w">Or log in with</span>
                <hr class="w-5 h-px bg-[#5D5A6F]" />
            </div>
            <app-login-social #loginSocialComp [typeShow]="1" />
            <div class="text-center mt-5">
                <span class="text-sm leading-5 text-ct-secondary" i18n="@@txt_dont_account">You already have an account.</span>&nbsp;
                <span class="text-sm font-medium leading-5 text-ct-brand-secondary cursor-pointer" i18n="@@txt_signup_now" (click)="openAuth()">Sign in now</span>
            </div>
            <div class="p-[5px] flex items-center justify-center pointer absolute right-5 top-5 bg-[#0a0f290a] rounded-lg" (click)="commonService.closeModal('modal-login')">
                <img class="w-3.5 h-3.5" src="/images/icons/ic_close_modal.svg" alt="">
            </div>
        </div>
    </div>
</app-modal>