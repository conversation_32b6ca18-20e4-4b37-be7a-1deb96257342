import { AbstractControl, ValidatorFn } from "@angular/forms";
import { SelectOption } from "../interfaces/common";

export const GENDER: SelectOption[] = [
    {
        value: null,
        label: $localize`:@@txt_choose_gender: Choose gender`
    },
    {
        value: 0,
        label: $localize`:@@txt_female: Female`
    },
    {
        value: 1,
        label: $localize`:@@txt_male: Male`
    },
    {
        value: -1,
        label: $localize`:@@txt_other: Other`
    }
];

export const FLAG_PHONES: SelectOption[] = [
    {
        value: '+84',
        icon: '/images/icons/ic_vi.png'
    }
];

export const SOCIAL_JOPTS: SelectOption[] = [
    {
        value: null,
        label: $localize `:@@txt_select_method:Select method`
    },
    {
        value: 'social_media_ads',
        label: $localize `:@@txt_social_media_ads:Social media advertising`
    },
    {
        value: 'facebook',
        label: 'Facebook'
    },
    {
        value: 'instagram',
        label: 'Instagram'
    },
    {
        value: 'threads',
        label: 'Threads'
    },
    {
        value: 'tiktok',
        label: 'Tik<PERSON>'
    },
    {
        value: 'friend_referral',
        label: $localize `:@@txt_recomend_by_friend:Recommended by a friend`
    },
    {
        value: 'other',
        label: $localize `:@@txt_other:Other`,
    }
];

export const TAKEN_JLPT: SelectOption[] = [
    {
        value: null,
        label: $localize `:@@txt_enter_answer:Enter answer`
    },
    {
        value: true,
        label: $localize `:@@txt_taken_jlpt_2:Yes, I have`
    },
    {
        value: 0,
        label: $localize `:@@txt_taken_jlpt_3:No, I haven’t`
    },
]

export const LEVEL_JLPTS: SelectOption[] = [
    {
        value: null,
        label: $localize `:@@txt_select_lv_jlpt:Select current level...`
    },
    {
        value: 1,
        label: 'JLPT N1'
    },
    {
        value: 2,
        label: 'JLPT N2'
    },
    {
        value: 3,
        label: 'JLPT N3'
    },
    {
        value: 4,
        label: 'JLPT N4'
    },
    {
        value: 5,
        label: 'JLPT N5'
    }
];

export const LEVEL_JOPTS: SelectOption[] = [
    {
        value: null,
        label: $localize `:@@txt_select_lv_jlpt:Select current level...`
    },
    {
        value: 5,
        label: 'JOPT A1'
    },
    {
        value: 4,
        label: 'JOPT A2'
    },
    {
        value: 3,
        label: 'JOPT B1'
    },
    {
        value: 2,
        label: 'JOPT B2'
    },
    {
        value: 1,
        label: 'JOPT C1'
    }
];

export const TEST_PURPOSES: SelectOption[] = [
    {
        value: null,
        label: $localize `:@@txt_select_purpose:Select purpose`
    },
    {
        value: 'Mục đích 1',
        label: 'Mục đích 1'
    },
    {
        value: 'Mục đích 2',
        label: 'Mục đích 2'
    },
];

export const JOBS: SelectOption[] = [
    {
        value: null,
        label: $localize `:@@txt_select_purpose:Select Occupation`
    },
    {
        value: 'pupil',
        label: $localize `:@@txt_pupil:Pupil`
    },
    {
        value: 'student',
        label: $localize `:@@txt_student:Student`
    },
    {
        value: 'teacher',
        label: $localize `:@@txt_teacher:Teacher`
    },
    {
        value: 'doctor',
        label: $localize `:@@txt_doctor:Doctor`
    },
    {
        value: 'developer',
        label: $localize `:@@txt_developer:Developer`
    },
    {
        value: 'designer',
        label: $localize `:@@txt_designer:Designer`
    },
    {
        value: 'other',
        label: $localize `:@@txt_other:Other`
    },
];

export const NOTES: string[] = [
    $localize `:@@txt_note_register_1:Take a photo of the front and back of the CCCD`,
    $localize `:@@txt_note_register_2:Full 4 corners, no cropping`,
    $localize `:@@txt_note_register_3:No blur, glare, or blurred text`,
    $localize `:@@txt_note_register_4:Do not use filters, beautify, or edit`,
]

export const NOTE_IMGS = [
    {
        image: '/images/icons/ic_cccd_1.png',
        label: $localize `:@@txt_good:Good`
    },
    {
        image: '/images/icons/ic_cccd_2.png',
        label: $localize `:@@txt_not_lacking:Not lacking`
    },
    {
        image: '/images/icons/ic_cccd_3.png',
        label: $localize `:@@txt_not_blur:Not blurred`
    },
    {
        image: '/images/icons/ic_cccd_4.png',
        label: $localize `:@@txt_no_glare:No glare`
    }
]

export const REQUIRE_ID_IMAGES: string[] = [
    $localize `:@@txt_require_id_image_1:Size: 4x6cm, JPG/JPEG/PNG format, capacity under 2MB`,
    $localize `:@@txt_require_id_image_2:Plain background, white background is recommended`,
    $localize `:@@txt_require_id_image_3:Do not wear glasses, do not use accessories such as sunglasses, headphones, hats, masks`,
    $localize `:@@txt_require_id_image_4:Photo clearly shows both shoulders, face occupies about 70-80% of the photo height`,
    $localize `:@@txt_require_id_image_5:Photo taken within the last 6 months from the time of registration`,
]

// Custom validator cho FormGroup
export function jlptConditionalValidator(keyrReq: string, keyRes: string): ValidatorFn {
  return (group: AbstractControl): { [key: string]: any } | null => {
    const hasTaken = group.get(keyrReq)?.value;
    const level = group.get(keyRes)?.value;

    if (hasTaken === true && !level) {
      return { levelJlptRequired: true };
    }

    return null;
  };
}