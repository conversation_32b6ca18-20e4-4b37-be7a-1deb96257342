<div class="alert br-10 {{ type }} {{ content ? 'show' : 'hide' }}">
    <div class="flex gap-2">
        @if(type == 'success'){
            <app-svg-icon name="noti_success_fill" fill="--fg-success-primary" [width]='22' [height]='22'></app-svg-icon>
        }@else if (type == 'error'){
            <app-svg-icon name="noti_err_fill" fill="--fg-error-primary" [width]='22' [height]='22'></app-svg-icon>
        }
        @else if (type == 'warning'){
            <app-svg-icon name="noti_warning_fill" fill="--fg-warning-primary" [width]='22' [height]='22'></app-svg-icon>
        }
        @else if (type == 'info'){
            <app-svg-icon name="noti_err_fill" fill="--fg-info-primary" [width]='22' [height]='22'></app-svg-icon>
        }
        @else if (type == 'brand'){
            <app-svg-icon name="noti_err_fill" fill="--fg-brand-primary" [width]='22' [height]='22'></app-svg-icon>
        }
        <div class="text-ct-white standard-txt-14 font-[500]">{{ content }}</div>
    </div>
 </div>