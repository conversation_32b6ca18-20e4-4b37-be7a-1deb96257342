export interface InforRegisterExam {
    name?: string,
    email?: string,
    gender?: number,
    phone?: string,
    day_of_birth?: number,
    month_of_birth?: number,
    year_of_birth?: number,
    register_info?: {
        address?: string,
        number_cccd?: string,
        job?: string
    },
    jopt_info?: {
        test_purpose?: string,
        jopt_target?: number,
        referral_source?: string,
        level_jlpt?: number,
        has_taken_jlpt?: false,
        total_years?: number,
        total_months?: number
    }
}

export interface RoundExam {
    id: string,
    name: string,
    exam_date: string,
    slots: ExameSession[]
}

export interface ExameSession {
    id: string,
    start_time: string,
    end_time: string
}