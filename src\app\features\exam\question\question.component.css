.exam-container {
  display: grid;
  grid-template-columns: 2fr 8fr 2fr;
  gap: 12px;
  padding: 16px;
  background: url('/images/header/backExam1.png') no-repeat center center;
  background-size: cover;
  min-height: 100vh;
}

/* Cột trái */
.column-left {
  background: rgba(255, 255, 255, 0.95);
  padding: 12px;
  border-radius: 8px;
  height: 85vh;
  max-height: 85vh;
  overflow-y: auto;
}

.section {
  margin-bottom: 16px;
}

h4 {
  margin-bottom: 8px;
}

.question-boxes21 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 6px;
}

.question-boxes11 {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 6px;
}

.question {
  background: #F9F9FB;
  border: 1px solid #F9F9FB;
  text-align: center;
  padding: 6px 0;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
}

.question.done {
  background: #e0e0e0;
  color: #555;
}

.question.active {
  background: #00cec9;
  color: #fff;
  font-weight: bold;
}


/* Cột giữa */
.column-center {
  margin-bottom: 10px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 20px;
  max-height: 55vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.column-center2 {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 20px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.question-section {
  padding: 12px 16px 30px 16px;
  border-radius: 12px;
  margin-bottom: 20px;
  background: var(--bg-white);
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: scroll;
}

.question-section {
  padding: 12px 16px 30px 16px;
  border-radius: 12px;
  margin-bottom: 20px;
  background: var(--bg-white);
  flex: 1;
  display: flex;
  flex-direction: column;
}

.question-header {
  margin-bottom: 24px;
  text-align: left;
}

.question-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--ct-primary);
  margin-bottom: 4px;
  margin-top: 0;
}

.question-subtitle {
  font-size: 14px;
  color: var(--ct-secondary);
  margin: 0;
}

.question-text {
  font-weight: 500;
  margin-bottom: 20px;
  font-size: 16px;
  color: var(--ct-primary);
}

.vocabulary-text {
  margin-top: 13%;
  font-size: 48px;
  font-weight: bold;
  margin-bottom: 15px;
  color: var(--ct-primary);
}

.vocabulary-text2 {
  text-align: left;
  font-size: 48px;
  font-weight: bold;
  margin-bottom: 15px;
  color: var(--ct-primary);
}

.vocabulary-mean {
  color: var(--ct-secondary);
  font-style: italic;
  font-size: 16px;
  margin: 0;
}


.record-section {
  flex: none;
  padding: 15px;
  width: 100%;
  border-radius: var(--Radius-radius-16, 16px);
}

.record-box {
  border: 2px solid #00cec9;
  border-radius: 8px;
  padding: 10px;
  text-align: center;
}

.icon-top {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}


.bar {
  width: 50%;
  height: 100%;
  background: #00b894;
  border-radius: 6px;
}

.record-text {
  font-size: 14px;
  color: #555;
}

.record-controls {
  margin-top: 12px;
  display: flex;
  justify-content: center;
  gap: 12px;
}

.record-button {
  background: transparent;
  border: none;
  padding: 12px;
  cursor: pointer;
  border-radius: 8px;
  outline: none;
}

.record-button:hover:not(.disabled) {
  background: rgba(9, 132, 227, 0.1);
}

.record-button:focus {
  outline: 2px solid rgba(0, 206, 201, 0.3);
  outline-offset: 2px;
}

.record-button.disabled {
  cursor: not-allowed !important;
  opacity: 0.6;
}

.record-icon {
  width: 60px;
  height: 60px;
  object-fit: contain;
  display: block;
}

.attempt-counter {
  font-size: 12px;
  color: #666;
  margin-left: 8px;
}

&:disabled {
  background: #b2bec3;
  cursor: not-allowed;
}


.icon-top {
  font-size: 24px;
  margin-bottom: 8px;
}

.record-bar {
  background: #dfe6e9;
  height: 8px;
  border-radius: 6px;
  margin: 12px 0;
}

.bar {
  width: 50%;
  height: 100%;
  background: #00b894;
  border-radius: 6px;
}

.record-text {
  font-size: 14px;
  color: #555;
}

/* Cột phải */
.column-right {
  background: rgba(255, 255, 255, 0.95);
  padding: 16px;
  border-radius: 12px;
  text-align: center;
  height: 85vh;
  max-height: 85vh;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.countdown-box {
  background: #ffeaea;
  border-radius: 8px;
  padding: 10px;
  margin-bottom: 16px;
  flex: none;
}

h3 {
  color: #e74c3c;
  font-size: 24px;
  margin-top: 4px;
}

.camera-box {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.camera-box img.user-camera {
  width: 100%;
  border-radius: 8px;
  margin: 8px 0;
}


.user-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  gap: var(--Spacing-spacing-12, 12px);
  align-self: stretch;
  border-radius: var(--Radius-radius-16, 16px);
  background: var(--Background-bg-white, #FFF);
  padding: 12px;
}

p {
  margin-bottom: 6px;
}

.next-btn {
  background: #00cec9;
  color: white;
  padding: 10px;
  border: none;
  border-radius: 8px;
  font-weight: bold;
  margin-top: 12px;
  width: 100%;
  flex: none;
}

&:hover {
  background: #00b894;
}

.text-infor {
  margin-top: 5%;
  color: var(--Foreground-fg-primary, #1C2024);

  /* Standard text/Body/14/Semibold */
  font-family: var(--Font-family-Body, Inter);
  font-size: var(--Font-size-md, 14px);
  font-style: normal;
  font-weight: var(--Font-weight-Semibold, 600);
  line-height: var(--Font-size-2xl, 20px);
  /* 142.857% */
  /* 142.857% */
}

.camera-view {
  height: 190px;
  align-self: stretch;
  border-radius: var(--Radius-radius-12, 12px);
  border: 3px solid var(--Border-bd-primary, #E0E1E6);
  width: 100%;
  object-fit: cover;
  flex: none;
}

.text-name {
  color: var(--Content-ct-secondary, #80838D);

  /* Standard text/Caption/Regular */
  font-family: var(--Font-family-Caption, Inter);
  font-size: var(--Font-size-sm, 12px);
  font-style: normal;
  font-weight: var(--Font-weight-Regular, 400);
  line-height: var(--Font-line-height-sm, 20px);
  /* 166.667% */
}

.content-question {
  flex: 1;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.content-question2 {
  color: var(--Content-ct-primary, #1C2024);

  /* JP text/Title/Regular */
  font-family: var(--Font-family-Title-JP, "Noto Sans JP");
  font-size: var(--Font-size-xl, 18px);
  font-style: normal;
  font-weight: var(--Font-weight-Regular, 400);
  line-height: var(--Font-line-height-lg, 28px);
  /* 155.556% */
}

.content-question3 {
  flex: 1;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.content-question4 {
  height: 90%;
  width: 100%;
  flex: 1;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* // Ghi âm câu tkloi bài 3 */
.audio-player-card {
  display: flex;
  width: 684px;
  padding: 0 20px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 40px;
  flex: 1 0 0;
  position: relative;

  @media (max-width: 991px) {
    width: 100%;
    max-width: 684px;
    padding: 0 16px;
    gap: 32px;
  }

  @media (max-width: 640px) {
    width: 100%;
    padding: 0 12px;
    gap: 24px;
  }
}

.sound-icon-container {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.sound-icon {
  width: 160px;
  height: 160px;
  position: relative;
}

.progress-controls {
  display: flex;
  align-items: center;
  gap: 16px;
  align-self: stretch;
  position: relative;

  @media (max-width: 991px) {
    gap: 12px;
  }

  @media (max-width: 640px) {
    gap: 8px;
    flex-direction: column;
  }
}

.play-pause-container {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  position: relative;

  @media (max-width: 640px) {
    align-self: center;
  }
}

.play-pause-button {
  display: flex;
  width: 20px;
  height: 20px;
  padding: 2.709px 2.708px 2.708px 2.708px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  background: none;
  border: none;
  cursor: pointer;
}

.pause-icon {
  width: 20px;
  height: 20px;
}

.progress-section {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1 0 0;
  position: relative;

  @media (max-width: 991px) {
    gap: 6px;
  }

  @media (max-width: 640px) {
    align-self: stretch;
    justify-content: space-between;
  }
}

.time-badge {
  display: flex;
  padding: 0 6px;
  flex-direction: column;
  align-items: flex-start;
  border-radius: 4px;
  position: relative;
  background-color: #f0f0f3;

  &.start-time {
    @media (max-width: 640px) {
      order: 1;
    }
  }

  &.end-time {
    @media (max-width: 640px) {
      order: 3;
    }
  }
}

.time-text {
  color: #80838d;
  text-align: center;
  position: relative;
  font: 500 12px/20px Inter;
  padding: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.progress-bar-container {
  display: flex;
  height: 6px;
  align-items: center;
  flex: 1 0 0;
  border-radius: 999px;
  position: relative;
  background-color: #f0f0f3;

  @media (max-width: 640px) {
    order: 2;
  }
}

.progress-bar {
  width: 100%;
  height: 6px;
  position: relative;
  border-radius: 999px;
  background-color: #f0f0f3;
}

.progress-fill {
  height: 6px;
  border-radius: 999px;
  position: absolute;
  left: 0;
  top: 0;
  background-color: #00b2a5;
  transition: width 0.2s ease;
}

.progress-handle {
  width: 10px;
  height: 10px;
  border-radius: 10px;
  background: #007d72;
  box-shadow: 0 0 11px 0 rgba(128, 131, 141, 0.17);
  position: absolute;
  top: -2px;
  transform: translateX(-50%);
  cursor: pointer;
  transition: left 0.2s ease;
}

.volume-button {
  display: flex;
  width: 20px;
  height: 20px;
  padding: 3.333px 1.667px;
  justify-content: center;
  align-items: center;
  position: relative;
  background: none;
  border: none;
  cursor: pointer;
}

.volume-icon {
  width: 20px;
  height: 20px;
}

.suggest {

  flex-direction: column;
  text-align: left;
  width: 100%;
  display: flex;
  gap: 20px;
  margin-top: 20px;
}

.suggest-column {
  flex: 1;
}

.suggest-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.suggest-list li {
  padding: 8px 0;
  font-size: 16px;
  color: var(--ct-primary);
  border-bottom: 1px solid #f0f0f0;
}

.suggest-list li:last-child {
  border-bottom: none;
}

.suggest-title {
  display: block;
  width: 100%;
  align-items: center;
  gap: 2px;
  align-self: stretch;
  color: var(--ct-error-primary);
}

.border-image {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
}

.image-question {
  max-width: 500px;
  max-height: 400px;
  width: auto;
  height: auto;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.waiting-opacity {
  opacity: 0.3;
  transition: opacity 0.3s ease;
}

.play-pause-button:disabled {
  cursor: not-allowed;
}

.play-pause-button:disabled .pause-icon {
  opacity: 0.5;
}




.replay-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background-color: #00b5ad;
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  cursor: pointer;
}

.countdown-container {
  position: relative;
  width: 32px;
  height: 32px;
}

.countdownListen {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-weight: bold;
}

.progress-ring__circle {
  stroke-dasharray: 88;
  /* 2πr với r=14 */
  stroke-dashoffset: 0;
  transform: rotate(-90deg);
  transform-origin: 50% 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    stroke-dashoffset: 0;
  }

  to {
    stroke-dashoffset: 88;
  }
}


.iconListen {
  width: 16px;
  /* kích thước vừa phải */
  height: 16px;
  object-fit: contain;
}

/* confirm-submit.component.css */
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.popup {
  background: white;
  border-radius: 12px;
  padding: 24px;
  width: 360px;
  text-align: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.icon {
  font-size: 36px;
  color: var(--Background-bg-brand-solid, #00bcd4);
  margin-bottom: 12px;
}

.title {
  margin: 0 0 8px;
  font-weight: 600;
  font-size: 18px;
}

.description {
  font-size: 14px;
  color: var(--Content-ct-secondary, #80838D);
  margin-bottom: 20px;
}

.buttons {
  display: flex;
  gap: 12px;
}

.cancel {
  flex: 1;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 6px;
  background: white;
  cursor: pointer;
}

.confirm {
display: flex;
width: 316px;
height: 40px;
padding: var(--Spacing-spacing-12, 12px) var(--Spacing-spacing-8, 8px);
justify-content: center;
align-items: center;
gap: var(--Spacing-spacing-8, 8px);
background: var(--Background-bg-brand-solid, #00b2a5);
}

.cancel:hover {
  background: #f5f5f5;
}

.colum-done{
margin-left: 90%;
margin-right: 90%;
margin-top: 20%;
display: flex;
width: 525px;
padding: var(--Spacing-spacing-16, 16px);
flex-direction: column;
align-items: center;
gap: var(--Spacing-spacing-32, 32px);
}

.text-title-done{
  color: var(--Content-ct-primary, #1C2024);
text-align: center;
/* Standard text/Title/18/Semibold */
font-family: var(--Font-family-Title, Inter);
font-size: var(--Font-size-xl, 18px);
font-style: normal;
font-weight: var(--Font-weight-Semibold, 600);
line-height: var(--Font-line-height-lg, 28px); /* 155.556% */
}
.text-content-done{
color: var(--Content-ct-secondary, #80838D);
text-align: center;

/* Standard text/Body/14/Regular */
font-family: var(--Font-family-Body, Inter);
font-size: var(--Font-size-md, 14px);
font-style: normal;
font-weight: var(--Font-weight-Regular, 400);
line-height: var(--Font-line-height-sm, 20px); /* 142.857% */
}

/* // driver
 */
 /* Driver.js custom styles giống như trong ảnh */
:host ::ng-deep .driver-overlay {
  background: rgba(0, 0, 0, 0.7) !important;
  backdrop-filter: blur(2px) !important;
}

:host ::ng-deep .driver-popover {
  background: white !important;
  border-radius: 8px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
  border: none !important;
  max-width: 400px !important;
  min-width: 300px !important;
  padding: 20px !important;
}

:host ::ng-deep .driver-popover-title {
  color: #333 !important;
  font-weight: 600 !important;
  font-size: 16px !important;
  margin-bottom: 12px !important;
  text-align: left !important;
}

:host ::ng-deep .driver-popover-description {
  color: #666 !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
  margin-bottom: 20px !important;
  text-align: left !important;
}

:host ::ng-deep .driver-popover-footer {
  display: flex !important;
  justify-content: flex-end !important;
  gap: 12px !important;
  margin-top: 20px !important;
}

:host ::ng-deep .driver-popover-next-btn {
  background: #00cec9 !important;
  color: white !important;
  border: none !important;
  border-radius: 6px !important;
  padding: 8px 16px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
}

:host ::ng-deep .driver-popover-next-btn:hover {
  background: #00b3ae !important;
}

:host ::ng-deep .driver-popover-prev-btn {
  background: transparent !important;
  color: #666 !important;
  border: 1px solid #ddd !important;
  border-radius: 6px !important;
  padding: 8px 16px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
}

:host ::ng-deep .driver-popover-prev-btn:hover {
  background: #f5f5f5 !important;
  border-color: #ccc !important;
}

:host ::ng-deep .driver-popover-close-btn {
  background: transparent !important;
  color: #999 !important;
  border: none !important;
  font-size: 18px !important;
  position: absolute !important;
  top: 15px !important;
  right: 15px !important;
  width: 24px !important;
  height: 24px !important;
  cursor: pointer !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

:host ::ng-deep .driver-popover-close-btn:hover {
  color: #666 !important;
}

/* Highlighted element styling */
:host ::ng-deep .driver-highlighted-element {
  border: 2px solid #00cec9 !important;
  border-radius: 4px !important;
  box-shadow: 0 0 0 4px rgba(0, 206, 201, 0.2) !important;
}

/* Progress indicator */
:host ::ng-deep .driver-popover-progress-text {
  position: absolute !important;
  top: 15px !important;
  left: 20px !important;
  color: #00cec9 !important;
  font-size: 12px !important;
  font-weight: 600 !important;
}
