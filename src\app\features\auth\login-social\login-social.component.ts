import { Component, ElementRef, EventEmitter, Input, Output, ViewChild } from '@angular/core';
import { CommonService } from '../../../shared/services/common.service';
import { UserService } from '../../../shared/services/user.service';
import { BroadcasterService } from '../../../shared/services/broadcaster.service';
import { LazyloadService } from '../../../shared/services/lazyload.service';
import { User } from '../../../common/interfaces/user';

declare var AppleID: any;

@Component({
    selector: 'app-login-social',
    standalone: true,
    imports: [],
    templateUrl: './login-social.component.html',
    styleUrl: './login-social.component.scss'
})
export class LoginSocialComponent {

    loading: boolean = false;
    loadDone: boolean = true;
    tokenClient: any;

    @Input() typeShow: number = 0;
    @Output() loginError = new EventEmitter();
    @ViewChild('loginGooGle') loginGooGle!: ElementRef<HTMLElement>;
    
    constructor(
        private lazyload: LazyloadService,
        private commonService: CommonService,
        private userService: UserService,
        private broadcaster: BroadcasterService
    ) {}

    ngOnInit() {
        this.lazyload.loadScript('https://accounts.google.com/gsi/client').subscribe( res => {
            this.initializeGoogleSignIn();
        });

        this.lazyload.loadScript('https://appleid.cdn-apple.com/appleauth/static/jsapi/appleid/1/en_US/appleid.auth.js').subscribe(_ => {
            if (this.commonService.getEnvironment() == 'client' && AppleID) {
                AppleID.auth.init({
                    clientId: 'net.migii.jlpt',
                    scope: 'name email',
                    redirectURI: 'https://jopt.migii.net/',
                    state: 'init',
                    nonce: 'production',
                    usePopup: true //or false defaults to false
                });
            }
        });

        if(this.commonService.getEnvironment() === 'client') {
            document
            .addEventListener('AppleIDSignInOnSuccess', (event) => {
                this.loadDone = true;
            });
            document.addEventListener('AppleIDSignInOnFailure', (event) => {
                this.loadDone = true;
            });
        }
        
    }

    initializeGoogleSignIn(): void {
        this.tokenClient = (window as any).google.accounts.oauth2.initTokenClient({
            client_id: "************-co3hc7kpdrmiu586jc6kkirhf3qkfjos.apps.googleusercontent.com",
            oneTapEnabled: false,
            prompt: 'select_account',
            scope: 'email profile https://www.googleapis.com/auth/userinfo.profile https://www.googleapis.com/auth/userinfo.email',
            callback: (tokenResponse: any) => {
                if (!tokenResponse.error) {
                    this.loginWithSocial(tokenResponse.access_token);
                }
            },
        });
    }

    handleCredentialResponse() {
        this.tokenClient.requestAccessToken();
    }

    public async loginWithApple() {
        if(!this.loadDone) return;
        setTimeout(async () => {
            try {
                const data = await AppleID.auth.signIn();
                this.loadDone = true;
                if (!data) {
                    this.commonService.showNotify($localize `:@@incorrect_login:Incorrect account or password`, 'error');
                    return;
                }
                this.loadDone = false;
                this.userService.loginWithApple('', data.authorization.id_token).subscribe({
                    next: res => {
                        this.loadDone = true;
                        this.loginSuccess(res.data);
                    },
                    error: err => {
                        this.loadDone = true;
                        if(err.error.statusCode === 403) {
                            this.loginError.emit();
                        }
                    }
                });
            } catch (error) {
                console.log(error)
            }
        }, 20);
    }

    loginWithSocial(idToken: string) {
        this.loadDone = false;
        this.userService.loginWithGoogle('', idToken).subscribe({
            next: res => {
                this.loginSuccess(res.data);
            },
            error: err => {
                this.loadDone = true;
                if(err.error.statusCode === 403) {
                    this.loginError.emit();
				}
            }
        });
    }

    loginSuccess(user: User | undefined) {
        this.loadDone = true;
		if (this.commonService.getEnvironment() === 'client') {
			if (user) {
                this.commonService.showNotify($localize `:@@login_success:Login successfully`, 'success');
                this.userService.setInforUser(user);
				setTimeout(() => {
                    this.commonService.closeAllModals();
				}, 100);
			} else {
				this.commonService.showNotify($localize `:@@txt_had_err: Something error!`, 'error');
			}
		}
	}
}
