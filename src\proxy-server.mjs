import { app as serverEn } from './server/en/server.mjs';
import { app as serverVi } from './server/vi/server.mjs';

const express = require('express');

function run() {
  const port = process.env.PORT || 4000;
  const server = express();

  server.use('/en', serverEn());
  server.use('/vi', serverVi());
  server.use('/', serverVi());

  server.listen(port, () => {
    console.log(`Node Express server listening on http://localhost:${port}`);
  });
}

run();