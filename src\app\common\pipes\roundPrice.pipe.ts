import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
	name: 'roundPrice',
	standalone: true
})
export class RoundPricePipe implements PipeTransform {

	transform(value: number, currency: string, unit?: string): string {
        if(currency === 'VND') {
            value = Math.round(value / 1000);
            if(unit === 'k') {
                return value.toLocaleString('en-US') + 'k';
            } else {
                return (value * 1000).toLocaleString('en-US');
            }
        } else if (currency === 'CNY' || currency === 'TWD') {
            return Math.round(value).toLocaleString('en-US');
        }
        return value.toLocaleString('en-US');
    }

}