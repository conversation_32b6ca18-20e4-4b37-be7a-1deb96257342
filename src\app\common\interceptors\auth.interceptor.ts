import { HttpInterceptorFn } from '@angular/common/http';
import { isPlatformBrowser } from '@angular/common';
import { PLATFORM_ID, inject } from '@angular/core';
import { AES , enc } from 'crypto-js';
import { SECRET_KEY } from '../constants/config';

export const authInterceptor: HttpInterceptorFn = (req, next) => {
    const platform = inject(PLATFORM_ID);
    let user;
    if (isPlatformBrowser(platform)) {
        const value = localStorage.getItem('data-init');
        if(value) {
            const bytes = AES.decrypt(value, SECRET_KEY);
            const dataString = bytes.toString(enc.Utf8);
            const data = JSON.parse(dataString.slice(0, dataString.length - SECRET_KEY.length));
            if (data) {
                user = data.user;
            }
        }
    }
    // check request in my server api or another api (google, ....) 
    const checkAPI = req.url.includes('api/');
    if (checkAPI && user && user.access_token) {
        // if user logged and request api
        const authReq = req.clone({
            headers: req.headers.set('Authorization', user.access_token)
        });
        return next(authReq);
    } else {
        return next(req);
    }
};
