import { Component, effect } from '@angular/core';
import { User } from '../../../../common/interfaces/user';
import { ObjectKey } from '../../../../common/interfaces/common';
import { CommonModule } from '@angular/common';
import { CommonService } from '../../../../shared/services/common.service';
import { ActivatedRoute, Router } from '@angular/router';
import { PaymentService } from '../../../../shared/services/payment.service';
import { RoundPricePipe } from "../../../../common/pipes/roundPrice.pipe";
import { OrderComponent } from "../order/order.component";
import { COUNTRY_BANK, examJoptFee } from '../../../../common/constants/schedule-pay';
import { UserCardComponent } from "../user-card/user-card.component";
import { ModalContactComponent } from "../../../../shared/components/modal-contact/modal-contact.component";
import { InforPay, InforPayExamJoptACB, QRCodePay, SaleItem, SalePackage } from '../../../../common/interfaces/payment';

@Component({
    selector: 'app-step-1',
    standalone: true,
    imports: [
        CommonModule,
        RoundPricePipe,
        OrderComponent,
        UserCardComponent,
        ModalContactComponent
    ],
    templateUrl: './step-1.component.html',
    styleUrls: [
        '../../../../shared/styles/button.css',
        './step-1.component.css'
    ]
})
export class Step1Component {

    langPriceNew: string = 'vi';
    packDetail: SalePackage<SaleItem | undefined> | undefined = examJoptFee;
    user: User | undefined;
    inforPay: InforPay | undefined;

    countryBank: ObjectKey<any> = COUNTRY_BANK;
    qRCode: QRCodePay | undefined;
    stepBankOther: string[] = [
        $localize`:@@txt_step_bank_f_1:Open the banking app`,
        $localize`:@@txt_step_bank_f_2:Enter and review account information, amount, transfer content`,
        $localize`:@@txt_step_bank_f_3:Make payment`,
        $localize`:@@txt_step_bank_f_4:Send me a successful transfer invoice`
    ];

    constructor(
        protected readonly commonService: CommonService,
        private router: Router,
        private route: ActivatedRoute,
        private paymentService: PaymentService
    ) {
        effect(() => {
            this.user = commonService.sUser();
        })
    }

    ngOnInit() {
        if(!this.paymentService.roudExamId || !this.paymentService.slotExamId) {
            this.router.navigate(['schedule']);
        }
    }

    getQrPay() {
        const data: InforPayExamJoptACB = {
            "product_id": "mytest_jopt",
            "price": 1970000,
            "attachment": {
                "slot_id": ''
            }
        }
    }

    roundPrice(value: number, currency: string) {
        if (currency === 'VND') {
            value = Math.round(value / 1000) * 1000;
        } else if (currency === 'CNY' || currency === 'TWD') {
            return Math.round(value);
        }
        return value;
    }

    copyText(text: string) {
        if(this.commonService.getEnvironment() === 'client') {
            navigator.clipboard.writeText(text).then(() => {
                const noti = $localize `:@@txt_copied:Đã sao chép vào bộ nhớ tạm!`
                this.commonService.showNotify(noti, 'success');
            }).catch((error) => {
                const noti = $localize `:@@txt_noti_error:Có lỗi xảy ra!`
                this.commonService.showNotify(noti, 'danger');
            });
        }
    }

    changePayMethod() {
        this.router.navigate(['../'], { relativeTo: this.route })
    }

    nextNotiPay() {
        this.router.navigate(['../notification'], { relativeTo: this.route });
    }

    downloadQr(url: string) {
        this.paymentService.downloadImage(url, 'Migii_jlpt_'+this.packDetail?.id);
    }

}
