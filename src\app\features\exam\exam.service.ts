import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { LoginCredentials } from '../../common/interfaces/exam';
@Injectable({
  providedIn: 'root'
})

export class ExamService {
  private apiUrl = 'https://api-jopt.migii.net/';
  private apiUrl2 = 'https://videosvc-jopt.migii.net/'
  constructor(private http: HttpClient) {

  }
  uploadChunk(file: File, sessionId: string, chunkIndex: number): Observable<any> {
    const url = `${this.apiUrl2}api/v1/upload/exam-session/video`;
    const formData = new FormData();
    sessionId = "ffb26cbb-65f6-4774-9b66-0e1b7e0cba43";
    formData.append('session_id', sessionId); 
    formData.append('chunk_video', file);
    formData.append('chunk_index', chunkIndex.toString());
    
    const headers = {
      'Authorization': `Bearer ${this.getAuthToken()}`
    };
    
    return this.http.post(url, formData, { headers });
  }

  getExamData(sessionId: string): Observable<any> {
    const url = `${this.apiUrl}api/v1/exam/${sessionId}`;
    const headers = {
      'Authorization': `Bearer ${this.getAuthToken()}`
    };
    return this.http.get(url, { headers });
  }


  uploadAudioAnswer(file: File, sessionId: string, questionId: string): Observable<any> {
    sessionId = "ffb26cbb-65f6-4774-9b66-0e1b7e0cba43";
    const formData = new FormData();
    formData.append('file', file);
    formData.append('session_id', sessionId);
    formData.append('question_id', questionId);
    const url = `${this.apiUrl}api/v1/exam-result`;
    
    const headers = {
      'Authorization': `Bearer ${this.getAuthToken()}`
    };
    
    return this.http.post(url, formData, { headers });
  }
  private getAuthToken(): string {
    const token = localStorage.getItem('token') || '';
    // Trả về token thuần, không có "Bearer " prefix
    return token.replace('Bearer ', '');
  }

   register(credentials: LoginCredentials): Observable<any> {
    return this.http.post<any>(this.apiUrl, credentials);
  }

}
