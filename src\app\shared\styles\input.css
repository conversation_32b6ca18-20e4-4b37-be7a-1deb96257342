.input{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    line-height: 20px;
    gap: 8px;
    background: var(--bg-white);
    outline: 1px solid var(--bg-secondary);
    border: none;
    box-sizing: border-box;
    font-size: 14px;
}
.input-err{
    outline: 1px solid var(--bd-error);
}
.input-success{
    outline: 1px solid var(--bg-brand-pressed);
}
.input-sm{
    padding: 6px 12px;
}
.input-md{
    padding: 8px 12px;
}
.input-lg{
    padding: 10px 12px;
}
.input:default{
    color: var(--ct-tertiary);
}
.input:hover{
    background: var(--bg-primary);
}
.input:focus{
    box-shadow: 0px 0px 0px 2px #FFF, 0px 0px 0px 4px var(--color-teal-5);

}

.select {
    border: 1px solid var(--bg-secondary);
    border-radius: 8px;
}
.select select {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 10px 12px;
    background-color: var(--bg-white);
    font-size: 14px;
    line-height: 20px;
    font-weight: 400;
    border-radius: 8px;
    outline: none;
}

.select:focus-within{
    border: 1px solid var(--bd-brand);
    box-shadow: 0px 0px 0px 2px #FFF, 0px 0px 0px 4px var(--color-teal-5);
}
.text-area{
    padding: 12px;
    border-radius: 8px;
    border: 1px solid var(--bd-secondary);
    background: var(--bg-white);
    font-size: 14px;
    line-height: 20px;
}