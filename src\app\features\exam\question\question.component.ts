import { Component, EventEmitter, input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { OnDestroy, OnInit } from '@angular/core';
import { Subscription, interval } from 'rxjs';
import { takeWhile } from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';
import { HttpClientModule } from '@angular/common/http';
import { ElementRef, Input, ViewChild } from '@angular/core';
import { ExamService } from '../exam.service';
import { Output } from '@angular/core';
import { Router } from '@angular/router';
import { time } from 'node:console';
import { driver, DriveStep } from 'driver.js';


@Component({
  selector: 'app-question',
  standalone: true,
  imports: [CommonModule, HttpClientModule],
  templateUrl: './question.component.html',
  styleUrl: './question.component.css'
})
export class QuestionComponent implements OnInit, OnDestroy {
  isConfirmEndExam: number =0;
  goHome: any = false;
  today = new Date();
  constructor(private http: HttpClient,
    private examService: ExamService,
    private router: Router,
  ) {
  }
  @Output() dataSentQuestion = new EventEmitter<any>();
  // @Input() autoSwitch: boolean = false;
  @Input() data: any;
  @Input() dataExamQuestion: any;
  isRecording = false;
  mediaRecorder!: MediaRecorder;
  audioChunks: Blob[] = [];
  questionGroups: any;
  private autoNextTimer: any;

  user = {
    name: 'Phùng Quang Anh',
    email: '<EMAIL>',
    birthdate: '30/10/2000',
    imageUrl: 'https://i.imgur.com/your-image.jpg',
  };

  remainingTime: number = 100 ; // Thời gian bắt đầu
  private timerSubscription!: Subscription;
  currentGroupIndex: number = 0;
  currentGroupIndexLevel2: number = 0; // Nhóm câu hỏi hiện tại con
  currentQuestionIndex: number = 0;
  timeToRecordAnswer: number = 7; // Câu hỏi hiện tại trong nhóm
  currentSubIndices: number[] = []; // Mảng chứa các index của các tầng con

  maxRecordingAttempts: number = 1; // Số lần được phép ghi âm
  currentRecordingAttempts: number = 0; // Số lần đã ghi âm

  get canRecord(): boolean {
    return this.currentRecordingAttempts < this.maxRecordingAttempts;
  }
  questionContent: any[][] = []
  // Getter để lấy nội dung câu hỏi hiện tại với safe access
  get currentQuestionContent() {
    if (this.questionContent &&
      this.questionContent[this.currentGroupIndex] &&
      this.questionContent[this.currentGroupIndex][this.currentGroupIndexLevel2] &&
      this.questionContent[this.currentGroupIndex][this.currentGroupIndexLevel2].parts &&
      this.questionContent[this.currentGroupIndex][this.currentGroupIndexLevel2].parts[this.currentQuestionIndex]) {
      return this.questionContent[this.currentGroupIndex][this.currentGroupIndexLevel2].parts[this.currentQuestionIndex];
    }
    return null;
  }

  async ngOnInit() {
    this.mergeData();
    this.startCountdown();
    const devices = await navigator.mediaDevices.enumerateDevices();
    const videoDevices = devices.filter(d => d.kind === 'videoinput');

    if (videoDevices.length > 0) {
      this.deviceId = videoDevices[0].deviceId; // 👈 lấy camera đầu tiên
    }
    this.startRecordingCam()
     setTimeout(() => {
      this.startTour();
    }, 1000);
  }

  mergeData() {
    if (this.data && this.data.step3 && !this.data.step3.isMergeData) {
      this.questionContent = Array.isArray(this.data.step3.questionContent)
        ? this.data.step3.questionContent
        : [];
      this.questionGroups = Array.isArray(this.data.step3.questionGroups)
        ? this.data.step3.questionGroups
        : [];
      this.currentGroupIndex = this.data.step3.currentGroupIndex;
      this.currentQuestionIndex = this.data.step3.currentQuestionIndex;
      this.currentGroupIndexLevel2 = this.data.step3.currentGroupIndexLevel2
    } else {
      this.questionGroups = Array.isArray(this.dataExamQuestion?.data?.skills)
        ? this.dataExamQuestion.data.skills
        : [];
      this.questionGroups.forEach((group: any, index: number) => {
        if (!this.questionContent[index]) {
          this.questionContent[index] = [];
        }
        group.parts.forEach((question: any, i: number) => {
          question.parts.forEach((part: any, j: number) => {
            part.status = 'todo';
            this.questionContent[index][i] = question;
          });

        });
      });
      if (this.questionGroups[0] && this.questionGroups[0].parts && this.questionGroups[0].parts[0] && this.questionGroups[0].parts[0].parts && this.questionGroups[0].parts[0].parts[0]) {
        this.questionGroups[0].parts[0].parts[0].status = 'active';
      }
      // Thêm dòng này để set active cho phần tử đầu tiên của group 2
      console.log("questionGroups", this.questionGroups);
      console.log("questionContent", this.questionContent);
    }
    this.loadAudioFromAPI();
    this.startAutoNextForGroup0();
  } // Merge data từ maindata

  startCountdown() {
    this.timerSubscription = interval(1000)
      .pipe(takeWhile(() => this.remainingTime > 0))
      .subscribe(() => {
        this.remainingTime--;
        if (this.remainingTime === 0) { 
          this.timeToRecordAnswer
          this.stopAudioRecording()
          this.nextExam();
        }
      });
  } // Đếm ngược thời gian làm bài 

  onTimeUp() {
    console.log('Hết giờ!');
    this.stopRecordingCam();

  }
  async toggleRecording() {
    if (!this.canRecord && !this.isRecording) {
      return;
    }

    if (this.isRecording) {
      this.stopAudioRecording();
    } else {
      this.currentRecordingAttempts++;
      await this.startAudioRecording();

      // Clear existing timeout nếu có
      if (this.recordingTimeout) {
        clearTimeout(this.recordingTimeout);
      }

      // Sử dụng Math.min để đảm bảo không vượt quá thời gian còn lại
      const timeToStop = Math.min(this.timeToRecordAnswer, this.remainingTime);
      
      this.recordingTimeout = setTimeout(() => {
        if (this.isRecording) {
          this.stopAudioRecording();
        }
      }, timeToStop * 1000);
    }
  }

  async startAudioRecording() {
    try {
      // Safe access cho data configuration
      const inputDeviceId = this.data && this.data.step1 && this.data.step1.selectedInputDevice;
      const inputVolume = (this.data && this.data.step1 && this.data.step1.inputVolume) || 50;

      const constraints: MediaStreamConstraints = {
        audio: {
          deviceId: inputDeviceId ? { exact: inputDeviceId } : undefined,
          echoCancellation: true,
          noiseSuppression: true
        }
      };

      this.audioStream = await navigator.mediaDevices.getUserMedia(constraints);

      this.audioChunks = [];
      
      // Sử dụng audio/wav thay vì audio/webm
      this.audioRecorder = new MediaRecorder(this.audioStream, {
        mimeType: 'audio/wav'
      });

      this.audioRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          this.audioChunks.push(event.data);
        }
      };

      this.audioRecorder.start();
      this.isRecording = true;

    } catch (error) {
      console.error('Lỗi khi bắt đầu ghi âm:', error);
    }
  }

  stopAudioRecording() {
    // Clear timeout khi dừng ghi âm
    if (this.recordingTimeout) {
      clearTimeout(this.recordingTimeout);
      this.recordingTimeout = null;
    }

    if (this.audioRecorder && this.audioRecorder.state === 'recording') {
      this.audioRecorder.stop();
    }

    if (this.audioStream) {
      this.audioStream.getTracks().forEach(track => track.stop());
    }

    if (this.recordingTimer) {
      this.recordingTimer.unsubscribe();
    }

    this.isRecording = false;
  }

  async sendAudioAnswer() {
    // if (this.audioChunks.length === 0) return;

    // Tạo file .wav thay vì .webm
    const audioBlob = new Blob(this.audioChunks, { type: 'audio/wav' });
    const audioFile = new File([audioBlob], `answer-${Date.now()}.wav`, {
      type: 'audio/wav'
    });

    this.examService.uploadAudioAnswer(
      audioFile, "sssId", this.currentQuestionContent.id
    ).subscribe({
      next: (res) => console.log('✅ Audio answer uploaded:', res),
      error: (err) => console.error('❌ Audio upload failed:', err)
    });
  }

  // Thêm properties cho audio recording
  private audioStream!: MediaStream;
  private audioRecorder!: MediaRecorder;
  private recordingTimer!: Subscription;
  private recordingTimeout: any; // Thêm property để track timeout
  // Default 60s

  uploadAudio(file: File) {
    const formData = new FormData();
    formData.append('audio', file);
    console.log(formData)
    // Gửi đến API
    this.http.post('https://your-api.com/upload', formData).subscribe({
      next: res => console.log('Gửi thành công', res),
      error: err => console.error('Gửi thất bại', err)
    });
  }

  async nextExam() {
    // Force gửi chunk hiện tại trước khi làm bất cứ gì
    if (this.mediaRecorderCam && this.mediaRecorderCam.state === 'recording') {
      console.log('🎬 Force sending current chunk before next exam');
      this.mediaRecorderCam.requestData();
      
      // Đợi chunk được gửi
      await new Promise(resolve => setTimeout(resolve, 200));
    }

    // Luôn gửi audio answer trước khi làm bất cứ gì
    this.sendAudioAnswer();
    
    // Safe access cho questionGroups
    if (this.questionGroups &&
      this.questionGroups[this.currentGroupIndex] &&
      this.questionGroups[this.currentGroupIndex].parts &&
      this.questionGroups[this.currentGroupIndex].parts[this.currentGroupIndexLevel2] &&
      this.questionGroups[this.currentGroupIndex].parts[this.currentGroupIndexLevel2].parts) {

      if (this.currentQuestionIndex < this.questionGroups[this.currentGroupIndex].parts[this.currentGroupIndexLevel2].parts.length - 1) {
        this.nextQuestion();
      } else if (this.currentGroupIndex < this.questionGroups.length - 1) {
        this.nextGroup();
      } else {
        // Đây là câu cuối cùng của bài thi
        console.log('Hoàn thành bài thi');
        if (this.remainingTime > 0 ){
        this.isConfirmEndExam = 2 ;
        } else {
          this.isConfirmEndExam = 1 ;
        }
        // Có thể emit event hoặc navigate đến trang kết quả
      }
    }
  }

  async nextQuestion() {
    // Force gửi chunk hiện tại nhưng không đợi
    if (this.mediaRecorderCam && this.mediaRecorderCam.state === 'recording') {
      console.log('🎬 Force sending current chunk before next question');
      this.mediaRecorderCam.requestData(); // Trigger ondataavailable ngay lập tức
      // Bỏ await - không đợi chunk được gửi
    }
    // Clear existing timer
    if (this.autoNextTimer) {
      clearTimeout(this.autoNextTimer);
    }
    // Reset recording attempts for new question
    this.currentRecordingAttempts = 0;
    // Reset video chunk index cho câu hỏi mới

    // Reset audio states for group 2
    if (this.currentGroupIndex === 2) {
      this.resetAudioStates();
    }

    // Safe access cho questionGroups
    if (this.questionGroups &&
      this.questionGroups[this.currentGroupIndex] &&
      this.questionGroups[this.currentGroupIndex].parts &&
      this.questionGroups[this.currentGroupIndex].parts[this.currentGroupIndexLevel2] &&
      this.questionGroups[this.currentGroupIndex].parts[this.currentGroupIndexLevel2].parts &&
      this.questionGroups[this.currentGroupIndex].parts[this.currentGroupIndexLevel2].parts[this.currentQuestionIndex]) {

      this.questionGroups[this.currentGroupIndex].parts[this.currentGroupIndexLevel2].parts[this.currentQuestionIndex].status = 'done';
      this.currentQuestionIndex++;

      if (this.questionGroups[this.currentGroupIndex].parts[this.currentGroupIndexLevel2].parts[this.currentQuestionIndex]) {
        this.questionGroups[this.currentGroupIndex].parts[this.currentGroupIndexLevel2].parts[this.currentQuestionIndex].status = 'active';
      }
    }

    // Load audio mới cho câu hỏi mới (nếu là group 2)
    if (this.currentGroupIndex === 2) {
      this.loadAudioFromAPI();
    }
    console.log(`Chuyển câu hỏi: Nhóm ${this.currentGroupIndex + 1}, Câu ${this.currentQuestionIndex + 1}`);
    
    // Bỏ sendAudioAnswer() ở đây vì đã gọi trong nextExam()
    // Start auto next for group 0
    this.startAutoNextForGroup0();
  }


  async nextGroup() {
    // Force gửi chunk hiện tại nhưng không đợi
    if (this.mediaRecorderCam && this.mediaRecorderCam.state === 'recording') {
      console.log('🎬 Force sending current chunk before next group');
      this.mediaRecorderCam.requestData(); // Trigger ondataavailable ngay lập tức
      // Bỏ await - không đợi chunk được gửi
    }

    // Reset recording attempts for new group
    this.currentRecordingAttempts = 0;
    // Reset video chunk index cho group mới

    // Bỏ sendAudioAnswer() ở đây vì đã gọi trong nextExam()

    // Safe access cho questionGroups
    if (this.questionGroups &&
      this.questionGroups[this.currentGroupIndex] &&
      this.questionGroups[this.currentGroupIndex].parts &&
      this.questionGroups[this.currentGroupIndex].parts[this.currentGroupIndexLevel2] &&
      this.questionGroups[this.currentGroupIndex].parts[this.currentGroupIndexLevel2].parts &&
      this.questionGroups[this.currentGroupIndex].parts[this.currentGroupIndexLevel2].parts[this.currentQuestionIndex]) {

      this.questionGroups[this.currentGroupIndex].parts[this.currentGroupIndexLevel2].parts[this.currentQuestionIndex].status = 'done';
    }

    // Safe access cho questionContent
    if (this.questionContent &&
      this.questionContent[this.currentGroupIndex] &&
      this.currentGroupIndexLevel2 < this.questionContent[this.currentGroupIndex].length - 1) {

      this.currentGroupIndexLevel2++;
      this.currentQuestionIndex = 0;

      // Safe access khi set active
      if (this.questionGroups &&
        this.questionGroups[this.currentGroupIndex] &&
        this.questionGroups[this.currentGroupIndex].parts &&
        this.questionGroups[this.currentGroupIndex].parts[this.currentGroupIndexLevel2] &&
        this.questionGroups[this.currentGroupIndex].parts[this.currentGroupIndexLevel2].parts &&
        this.questionGroups[this.currentGroupIndex].parts[this.currentGroupIndexLevel2].parts[this.currentQuestionIndex]) {

        this.questionGroups[this.currentGroupIndex].parts[this.currentGroupIndexLevel2].parts[this.currentQuestionIndex].status = 'active';
      }

      this.dataSentQuestion.emit({
        chuckIndex: this.videoChunkIndex,
        examNo: this.currentGroupIndex + 1,
        isMergeData: false,
        questionContent: this.questionContent,
        questionGroups: this.questionGroups,
        currentGroupIndex: this.currentGroupIndex,
        currentQuestionIndex: this.currentQuestionIndex,
        currentGroupIndexLevel2: this.currentGroupIndexLevel2,
        timeRemaning: this.remainingTime
      });
    } else {
      this.currentGroupIndexLevel2 = 0;
      this.currentQuestionIndex = 0;
      this.currentGroupIndex++;

      // Safe access khi chuyển group
      if (this.questionGroups &&
        this.questionGroups[this.currentGroupIndex] &&
        this.questionGroups[this.currentGroupIndex].parts &&
        this.questionGroups[this.currentGroupIndex].parts[this.currentGroupIndexLevel2] &&
        this.questionGroups[this.currentGroupIndex].parts[this.currentGroupIndexLevel2].parts &&
        this.questionGroups[this.currentGroupIndex].parts[this.currentGroupIndexLevel2].parts[this.currentQuestionIndex]) {

        this.questionGroups[this.currentGroupIndex].parts[this.currentGroupIndexLevel2].parts[this.currentQuestionIndex].status = 'active';
      }

      this.dataSentQuestion.emit({
        chuckIndex: this.videoChunkIndex,
        examNo: this.currentGroupIndex + 1,
        isMergeData: false,
        questionContent: this.questionContent,
        questionGroups: this.questionGroups,
        currentGroupIndex: this.currentGroupIndex,
        currentQuestionIndex: this.currentQuestionIndex,
        currentGroupIndexLevel2: this.currentGroupIndexLevel2,
        timeRemaning: this.remainingTime
      });
    }
    // Cập nhật trạng thái câu hỏi đầu tiên của nhóm mới
    // this.questionGroups[this.currentGroupIndex].parts[this.currentGroupIndexLevel2].parts[this.currentQuestionIndex].status = 'active';
  }


  @ViewChild('videoElement') videoElement!: ElementRef<HTMLVideoElement>;
  deviceId!: string;

  private mediaRecorderCam!: MediaRecorder;
  private mediaStream!: MediaStream;
  private uploadedChunks: File[] = [];
  // Thêm property để track chunk index cho video
  @Input() videoChunkIndex: number = 1;

  async startRecordingCam() {
    if (!this.deviceId) {
      console.error('Thiếu deviceId');
      return;
    }

    this.mediaStream = await navigator.mediaDevices.getUserMedia({
      video: { deviceId: { exact: this.deviceId } },
      audio: false
    });
    
    if (this.videoElement && this.videoElement.nativeElement){
      this.videoElement.nativeElement.srcObject = this.mediaStream;
    } else {
      console.error('Không tìm thấy video element');
      return;
    }
    
    this.uploadedChunks = [];

    // Mặc định sử dụng mp4
    let mimeType = 'video/mp4';
    if (!MediaRecorder.isTypeSupported(mimeType)) {
      mimeType = 'video/webm'; // Fallback nếu mp4 không support
    }

    console.log('🎥 Using mimeType:', mimeType);

    this.mediaRecorderCam = new MediaRecorder(this.mediaStream, {
      mimeType: mimeType
    });

    this.mediaRecorderCam.ondataavailable = async (event) => {
      if (event.data.size > 0) {
        const chunk = event.data;
        const file = new File([chunk], `chunk-${Date.now()}.mp4`, {
          type: 'video/mp4'
        });

        this.uploadedChunks.push(file);
        await this.sendChunkToApi(file);
      }
    };

    this.mediaRecorderCam.start(10000);
  }

  async stopRecordingCam() {
    this.mediaRecorderCam.stop();
    this.mediaStream?.getTracks().forEach(track => track.stop());

    if (this.videoElement?.nativeElement) {
      this.videoElement.nativeElement.srcObject = null;
    }

    console.log('⏹️ Dừng quay. Tổng số chunk đã lưu:', this.uploadedChunks.length);
  }

  async sendChunkToApi(file: File): Promise<void> {
    console.log('📹 Sending chunk with index:', this.videoChunkIndex);
    this.examService.uploadChunk(file, 'SssId', this.videoChunkIndex).subscribe({
      next: (res) => {
        console.log('✅ Upload success:', res);
        this.videoChunkIndex++;
      },
      error: (err) => console.error('❌ Upload error:', err)
    });
  }

  ngOnDestroy() {
    // Clear timeout khi destroy component
    if (this.recordingTimeout) {
      clearTimeout(this.recordingTimeout);
    }
    
    if (this.autoNextTimer) {
      clearTimeout(this.autoNextTimer);
    }
    this.mediaStream?.getTracks().forEach(track => track.stop());
    this.timerSubscription?.unsubscribe();

    // Loa 
    this.mediaStream?.getTracks().forEach(track => track.stop());
    this.timerSubscription?.unsubscribe();

    this.clearProgressInterval();
    if (this.audioElement) {
      this.audioElement.pause();
      this.audioElement = null;
    }
    if (this.waitTimer) {
      clearTimeout(this.waitTimer);
    }
  }

  // Ghi âm câu trả lời bài 3
  currentTime = '0:00';
  totalTime = '0:00';
  isPlaying = false;
  progress = 0; // percentage
  private progressInterval: any;

  // Thêm các properties mới
  maxListenAttempts: number = 2; // Số lần được phép nghe
  currentListenAttempts: number = 0; // Số lần đã nghe
  canListen: boolean = true; // Có thể nghe hay không
  waitingToListen: boolean = false; // Đang chờ để nghe lại
  waitTimer: any; // Timer cho việc chờ
  countdown = 3
  get canPlayAudio(): boolean {
    return this.currentListenAttempts < this.maxListenAttempts && this.canListen && !this.waitingToListen;
  }

  //  Đoạn phát audio
  audioElement: HTMLAudioElement | null = null;
  audioUrl: string = '';
  isAudioLoaded: boolean = false;


  // Nhận audio URL từ API


  loadAudioFromAPI() {
    // Lấy URL audio động từ currentQuestionContent với safe access
    if (this.currentGroupIndex === 2 &&
      this.currentQuestionContent &&
      this.currentQuestionContent.content &&
      this.currentQuestionContent.content.audio) {
      this.audioUrl = this.currentQuestionContent.content.audio;
    } else {
      // Fallback URL nếu không có audio
      console.log('Không có audio');
    }
    this.initializeAudio();
  }

  async initializeAudio() {
    if (this.audioElement) {
      this.audioElement.pause();
      this.audioElement = null;
    }

    this.audioElement = new Audio(this.audioUrl);

    // Safe access cho data
    const outputDeviceId = this.data && this.data.step1 && this.data.step1.selectedOutputDevice;
    if ('setSinkId' in this.audioElement && outputDeviceId) {
      await (this.audioElement as any).setSinkId(outputDeviceId);
    }

    // Safe access cho outputVolume
    const outputVolume = (this.data && this.data.step1 && this.data.step1.outputVolume) || 50;
    this.audioElement.volume = outputVolume / 100;
    console.log("🔹 Gắn listener loadedmetadata");
    this.audioElement.addEventListener('loadedmetadata', () => {
      console.log("✅ Sự kiện loadedmetadata đã bắn");
      console.log("⏳ Duration:", this.audioElement?.duration);
      this.totalTime = this.formatTime(this.audioElement!.duration);
      this.isAudioLoaded = true;
    });

    // Sử dụng requestAnimationFrame để cập nhật mượt mà hơn
    this.audioElement.addEventListener('timeupdate', () => {
      if (this.audioElement && !this.audioElement.paused) {
        requestAnimationFrame(() => {
          this.currentTime = this.formatTime(this.audioElement!.currentTime);
          this.progress = (this.audioElement!.currentTime / this.audioElement!.duration) * 100;
        });
      }
    });

    this.audioElement.addEventListener('ended', () => {
      this.isPlaying = false;
      this.progress = 100;
      this.currentListenAttempts++; // Tăng số lần đã nghe

      setTimeout(() => {
        this.progress = 0;
        this.currentTime = '0:00';
      }, 200);

      // Nếu chưa hết lượt nghe, bắt đầu đếm thời gian chờ
      if (this.currentListenAttempts < this.maxListenAttempts) {
        this.startWaitTimer();
      } else {
        this.canListen = false; // Hết lượt nghe
      }
    });
  }

  togglePlayPause(): void {
    if (!this.audioElement || !this.isAudioLoaded || !this.canPlayAudio) return;

    if (this.isPlaying) {
      this.audioElement.pause();
      this.clearProgressInterval();
    } else {
      this.audioElement.play();
      this.startProgressInterval();
    }
    this.isPlaying = !this.isPlaying;
  }

  private startProgressInterval() {
    this.clearProgressInterval();
    this.progressInterval = setInterval(() => {
      if (this.audioElement && !this.audioElement.paused) {
        this.currentTime = this.formatTime(this.audioElement.currentTime);
        this.progress = (this.audioElement.currentTime / this.audioElement.duration) * 100;
      }
    }, 50); // Cập nhật mỗi 50ms
  }

  private clearProgressInterval() {
    if (this.progressInterval) {
      clearInterval(this.progressInterval);
      this.progressInterval = null;
    }
  }

  onProgressChange(event: Event): void {
    if (!this.audioElement || !this.isAudioLoaded) return;

    const target = event.target as HTMLInputElement;
    const newProgress = parseInt(target.value, 10);
    const newTime = (newProgress / 100) * this.audioElement.duration;

    this.audioElement.currentTime = newTime;
    this.progress = newProgress;
  }

  seekAudio(event: MouseEvent): void {

    // if (!this.audioElement || !this.isAudioLoaded) return;
    // const progressBar = event.currentTarget as HTMLElement;
    // const rect = progressBar.getBoundingClientRect();
    // const clickX = event.clientX - rect.left;
    // const newProgress = (clickX / rect.width) * 100;
    // const newTime = (newProgress / 100) * this.audioElement.duration;

    // this.audioElement.currentTime = newTime;
    // this.progress = newProgress;
  }

  private formatTime(seconds: number): string {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  }

  private startWaitTimer() {
    this.waitingToListen = true;
    this.countdown = 3;
    let remaining = this.countdown;

    clearInterval(this.waitTimer);

    this.waitTimer = setInterval(() => {
      remaining--;
      this.countdown = remaining;

      if (remaining <= 0) {
        clearInterval(this.waitTimer);
        this.waitingToListen = false;
      }
    }, 1000);
  }

  private startAutoNextForGroup0() {
    // const timeToRead = this.data?.step2?.timeToReadParagraph;
    if (this.currentGroupIndex === 0) {
      this.timeToRecordAnswer = 10;
      const timeToRead = this.data?.step2?.timeToReadSingleWord;
      this.autoNextTimer = setTimeout(() => {
        this.nextExam();
      }, timeToRead * 1000); // 7s để ghi âm từ 
    } else if (this.currentGroupIndex === 1) {
      this.timeToRecordAnswer = 180;
      // this.timeToRead = this.data?.step2?.timeToReadParagraph;  /// 2 phút để ghi âm đoạn đọc 
    } else if (this.currentGroupIndex === 2) {
      this.timeToRecordAnswer = 60;
    } else if (this.currentGroupIndex === 3) {
      // this.timeToRead = this.data?.step2?.timeToReadParagraph;
      this.timeToRecordAnswer = 240
    } 
    this.remainingTime = this.timeToRecordAnswer;
  }

  private resetAudioStates() {
    // Stop current audio if playing
    if (this.audioElement) {
      this.audioElement.pause();
      this.audioElement.currentTime = 0;
    }

    // Reset audio control states
    this.isPlaying = false;
    this.progress = 0;
    this.currentTime = '0:00';
    this.currentListenAttempts = 0;
    this.canListen = true;
    this.waitingToListen = false;

    // Clear wait timer
    if (this.waitTimer) {
      clearTimeout(this.waitTimer);
      this.waitTimer = null;
    }

    // Clear progress interval
    this.clearProgressInterval();
  }



  onCancel() {
      console.log('Xác nhận nộp bài sớm')
  }
  onGoHome() {
    this.router.navigate(['/home']);
  }
 

  // Thêm driver.js setup
 private driverObj = driver({
  showProgress: true,
  showButtons: ['next', 'previous', 'close'],
  overlayColor: 'rgba(0, 0, 0, 0.7)',
  smoothScroll: true,
  allowClose: true,
  steps: [
    {
      element: '.column-left',
      popover: {
        title: 'Danh sách câu hỏi',
        description: 'Vui lòng làm đầy đủ các bài tập trắc nghiệm làm cho câu hỏi của bạn được đầy đủ và chính xác nhất.',
        side: "right",
        align: 'center',
        showButtons: ['next', 'close']
      }
    },
    {
      element: '.column-center',
      popover: {
        title: 'Nội dung câu hỏi',
        description: 'Đây là khu vực hiển thị nội dung chi tiết của câu hỏi hiện tại. Hãy đọc kỹ đề bài trước khi trả lời.',
        side: "bottom",
        align: 'center',
        showButtons: ['next', 'previous', 'close']
      }
    },
    {
      element: '.column-center2',
      popover: {
        title: 'Khu vực ghi âm',
        description: 'Nhấn vào nút mic để bắt đầu ghi âm câu trả lời của bạn. Đảm bảo micro hoạt động tốt.',
        side: "top",
        align: 'center',
        showButtons: ['previous', 'close']
      }
    }
  ]
});

  startTour() {
    this.driverObj.drive();
  }

  restartTour() {
    this.driverObj.drive();
  }
}
