@let userJoptInfo = commonService.sUserJopt();
<div class="flex items-center">
    <h2 class="text-lg m-0 font-semibold mr-2" i18n="@@txt_your_info">Your information</h2>
    <div class="bg-[var(--color-teal-3)] text-ct-brand-secondary text-xs leading-5 px-2 py-0 rounded-[99px]" i18n="@@txt_default">Mặc định</div>
</div>
<div class="flex items-start mt-3">
    @if(userJoptInfo && userJoptInfo.user) {
        <img class="mr-3" width="48" height="48" [src]="'/images/icons/ic_avatar.png'" alt="Avatar">
        <div>
            @if(userJoptInfo.user.name) {
                <div class="flex mb-2">
                    <img class="mr-1.5" src="/images/icons/ic_user.svg" alt="Username">
                    <span class="text-sm text-ct-secondary">{{ userJoptInfo.user.name }}</span>
                </div>
            }
            @if(userJoptInfo.user.phone) {
                <div class="flex mb-2">
                    <img class="mr-1.5" src="/images/icons/ic_phone.svg" alt="phone">
                    <span class="text-sm text-ct-secondary">{{ userJoptInfo.user.phone }}</span>
                </div>
            }
            @if(userJoptInfo.user.email) {
                <div class="flex mb-2">
                    <img class="mr-1.5" src="/images/icons/ic_mail.svg" alt="mail">
                    <span class="text-sm text-ct-secondary">{{ userJoptInfo.user.email }}</span>
                </div>
            }
            @if(userJoptInfo.jopt_info.number_cccd) {
                <div class="flex mb-2">
                    <img class="mr-1.5" src="/images/icons/student-card.svg" alt="mail">
                    <span class="text-sm text-ct-secondary">{{ userJoptInfo.jopt_info.number_cccd }}</span>
                </div>
            }
            @if(userJoptInfo.jopt_info.address) {
                <div class="flex items-start">
                    <img class="mr-1.5 mt-0.5" src="/images/icons/location-04.svg" alt="Address">
                    <span class="text-sm text-ct-secondary">{{ userJoptInfo.jopt_info.address }}</span>
                </div>ƒ
            }
        </div>
    }
</div>