import { Component, ElementRef, Input, SimpleChanges, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { DomSanitizer } from '@angular/platform-browser';
import { CommonService } from '../../services/common.service';
@Component({
    selector: 'app-svg-icon',
    standalone: true,
    imports: [CommonModule],
    templateUrl: './svg-icon.component.html',
    styleUrls: ['./svg-icon.component.css']
})
export class SvgIconComponent {
    @Input() name?: string;
    @Input() cache?: boolean = true;
    @Input() width?: number = 24;
    @Input() height?: number = 24;
    @Input() folder?: string = 'icons';
    @Input() fill?: string = '--icon-subtitle';
    public svgIcon: any;
    @ViewChild('mySvg') mySvgElementRef!: ElementRef;

    constructor(
        readonly httpClient: HttpClient,
        readonly sanitizer: DomSanitizer,
        readonly commonService: CommonService
    ) {
    }

    ngOnInit(): void {
        if (this.name && this.commonService.svgCache.has(this.name)) {
            this.svgIcon = this.sanitizer.bypassSecurityTrustHtml(this.commonService.svgCache.get(this.name)!);
        }
    }

    public ngOnChanges(changes: SimpleChanges): void {
        if (changes['name'] && this.name  || changes['fill'] && this.fill) {
            this.loadSvg();
        }
    }

    private loadSvg() {
        if (!this.name) {
            this.svgIcon = '';
            return;
        }

        if (!this.commonService.svgCache.has(this.name)) {
            this.httpClient
            .get(`/images/${this.folder}/${this.name}.svg`, { responseType: 'text' })
            .subscribe(value => {
                this.svgIcon = this.sanitizer.bypassSecurityTrustHtml(value);
                if(this.cache) {
                    this.commonService.svgCache.set(this.name!, value);
                }
                this.applyFill();
            });
        } else {
            this.applyFill();
        }
    }

    private applyFill() {
        if (this.fill && this.fill != 'none') {
            setTimeout(() => {
                const svgElement = this.mySvgElementRef.nativeElement.querySelector('svg');
                if(svgElement) {
                    svgElement.style.fill = `var(${this.fill})`;
                }
            }, 10);
        }
    }
}
