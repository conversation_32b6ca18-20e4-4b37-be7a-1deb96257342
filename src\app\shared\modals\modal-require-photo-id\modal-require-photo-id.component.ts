import { Component } from '@angular/core';
import { ModalComponent } from "../../components/modal/modal.component";
import { CommonService } from '../../services/common.service';

@Component({
    selector: 'app-modal-require-photo-id',
    standalone: true,
    imports: [ModalComponent],
    templateUrl: './modal-require-photo-id.component.html',
    styleUrl: './modal-require-photo-id.component.css'
})
export class ModalRequirePhotoIdComponent {

    constructor(
        protected readonly commonService: CommonService
    ) {}

    ngOnInit(){}
}
