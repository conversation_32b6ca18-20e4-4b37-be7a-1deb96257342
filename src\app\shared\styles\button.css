.btn {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    gap: 4px;
    border-radius: 8px;
    cursor: pointer;
    font-family: var(--font-action);
    flex-shrink: 0;
    border: none;
    font-weight: 500;
}

.btn-sm {
    padding: 4px 8px;
    line-height: 20px;
    font-size: 12px;
}

.btn-md {
    padding: 8px;
    height: 36px;
    font-size: 12px;
}

.btn-lg {
    padding: 12px 8px;
    font-size: 14px;
    line-height: 20px;
}

.btn-elg {
    padding: 12px 8px;
    font-size: 16px;
    line-height: 24px;
}

.btn-inverse-dark {
    background-color: var(--bg-primary);
    color: var(--ct-secondary);
}

.btn-inverse-dark:hover {
    color: var(--ct-primary);
}

.btn-inverse-dark:focus {
    border: 1px solid var(--bd-dark);
    outline-offset: 2px;
    outline: 2px solid var(--bd-dark_alt);
}

.btn-inverse-primary {
    background-color: var(--bg-brand-secondary);
    color: var(--ct-brand-secondary);
}

.btn-inverse-primary:hover {
    color: var(--ct-brand-primary);
}

.btn-inverse-primary:focus {
    border: 1px solid var(--bd-brand);
    outline-offset: 2px;
    outline: 2px solid var(--bg-brand-secondary);
}

.btn-solid,
.btn-solid-error,
.btn-solid-success {
    color: var(--ct-white);
}

.btn-solid:hover,
.btn-solid-error:hover,
.btn-solid-success:hover {
    background: var(--bd-secondary);
}

.btn-solid-primary {
    background-color: var(--bg-brand-solid);
}

.btn-solid-light {
    background-color: var(--bg-white);
    color: var(--ct-primary) !important;
}

.btn-solid-light-custom {
    background-color: var(--bg-tertiary);
}

.btn-solid-light-custom:hover,
.btn-solid-light:hover {
    background-color: var(--bg-secondary);
}

.btn-solid-light-custom:focus,
.btn-solid-light:focus {
    background-color: var(--bg-tertiary);
    box-shadow: 0px 0px 0px 2px #fff, 0px 0px 0px 4px var(--color-gray-4);
}

.btn-solid-dark {
    background-color: var(--bg-dark-solid);
}

.btn-solid-dark:hover {
    background-color: var(--bg-dark-hover);
}

.btn-solid-dark:focus {
    background-color: var(--bg-dark-solid);
    box-shadow: 0px 0px 0px 4px var(--color-gray-4);
    box-shadow: 0px 0px 0px 2px #fff;
}

.btn-solid-error {
    background-color: var(--bg-error-solid);
}

.btn-solid-error:hover {
    background-color: var(--bg-error-hover);
}

.btn-solid-error:focus {
    background-color: var(--bg-error-solid);
    box-shadow: 0px 0px 0px 2px #fff, 0px 0px 0px 4px #ffeceb;
}

.btn-solid-success {
    background-color: var(--bg-success-solid);
}

.btn-solid-success:hover {
    background-color: var(--bg-success-hover);
}

.btn-solid-success:focus {
    background-color: var(--bg-success-solid);
    box-shadow: 0px 0px 0px 2px #fff, 0px 0px 0px 4px var(--color-green-4);
}

.btn-on-color-light {
    color: var(--ct-primary);
    background-color: var(--bg-white);
}

.btn-on-color-light:hover {
    background-color: var(--bg-secondary);
}

.btn-on-color-light:focus {
    background-color: var(--bg-secondary);
    box-shadow: 0px 1px 2px 0px var(--bg-dark_alt), 0px 0px 0px 1px var(--bg-dark_alt);
}

.btn-on-color-dark {
    color: var(--ct-primary);
    background-color: var(--bg-primary);
}

.btn-on-color-dark-pressed {
    color: var(--ct-primary);
    background-color: var(--bg-tertiary);
}

.btn-on-color-dark:hover {
    background-color: var(--bg-secondary);
}

.btn-on-color-dark:focus {
    background-color: var(--bg-white);
    box-shadow: 0px 1px 2px 0px var(--bg-dark-solid), 0px 0px 0px 1px var(--bg-dark-solid);
}

.btn-brand-pressed {
    background: linear-gradient(180deg, rgba(2, 126, 128, 0.00) 0%, rgba(2, 126, 128, 0.50) 50%, #088385 100%), linear-gradient(180deg, rgba(255, 255, 255, 0.20) 0%, rgba(255, 255, 255, 0.08) 100%), linear-gradient(180deg, #21c99d 0%, #11bd9e 50%, #00b2a5 100%);
    color: var(--ct-white);
    box-shadow: 0px 1px 2px 0px rgba(17, 79, 75, 0.40), 0px 0px 0px 1px rgba(1, 153, 143, 0.76);
}

.btn-brand {
    color: var(--ct-white);
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.16) 0%, rgba(255, 255, 255, 0.00) 100%), linear-gradient(180deg, #21c99d 0%, #11bd9e 50%, #00b2a5 100%);
    box-shadow: 0px 1px 2px 0px rgba(17, 79, 75, 0.40), 0px 0px 0px 1px rgba(1, 153, 143, 0.76);
    line-height: 20px;
}

.btn-brand:hover {
    background: linear-gradient(180deg, rgba(2, 126, 128, 0.00) 0%, rgba(2, 126, 128, 0.50) 50%, #088385 100%), linear-gradient(180deg, rgba(255, 255, 255, 0.20) 0%, rgba(255, 255, 255, 0.08) 100%), linear-gradient(180deg, #21c99d 0%, #11bd9e 50%, #00b2a5 100%);
}

.btn-question-selected,
.btn-brand:focus,
.btn-brand-selected {
    box-shadow: 0px 0px 0px 2px #fff, 0px 0px 0px 4px var(--Teal-5, #aaede4);
    outline-offset: 2px;
    outline: 2px solid #aaede4;
}

.btn-brand:focus,
.btn-brand-selected {
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.16) 0%, rgba(255, 255, 255, 0.00) 100%), linear-gradient(180deg, #21c99d 0%, #11bd9e 50%, #00b2a5 100%);
}

.btn-correct-light {
    background-color: var(--bg-success-solid) !important;
    color: var(--ct-white);
}

.btn-correct {
    background-color: var(--bg-brand-hover) !important;
    color: var(--ct-white);
}

.btn-incorrect {
    background: var(--bg-error-solid) !important;
    color: var(--ct-white);
    box-shadow: 0px 1px 2px 0px rgba(255, 0, 0, 0.40), 0px 0px 0px 1px rgba(255, 0, 0, 0.76);
}