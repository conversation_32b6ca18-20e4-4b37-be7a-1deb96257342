{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"migii-jopt-web": {"i18n": {"sourceLocale": "en", "locales": {"vi": {"translation": "src/locale/messages.vi.json", "baseHref": "/vi/"}}}, "projectType": "application", "schematics": {}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"localize": true, "outputPath": "dist/migii-jopt-web", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js", "@angular/localize/init"], "tsConfig": "tsconfig.app.json", "assets": [{"glob": "**/*", "input": "public"}], "styles": ["src/styles.css"], "scripts": [], "server": "src/main.server.ts", "prerender": true, "ssr": {"entry": "server.ts"}}, "configurations": {"vi": {"localize": ["vi"], "outputPath": "dist/migii-jopt-web/vi", "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.development.ts"}]}, "production": {"budgets": [{"type": "initial", "maximumWarning": "500kB", "maximumError": "1MB"}, {"type": "anyComponentStyle", "maximumWarning": "2kB", "maximumError": "4kB"}], "outputHashing": "all"}, "development": {"localize": ["en"], "baseHref": "/en/", "optimization": false, "extractLicenses": false, "sourceMap": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.development.ts"}]}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"vi": {"buildTarget": "migii-jopt-web:build:vi"}, "production": {"buildTarget": "migii-jopt-web:build:production"}, "development": {"buildTarget": "migii-jopt-web:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "migii-jopt-web:build", "outputPath": "src/locale", "format": "json"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing", "@angular/localize/init"], "tsConfig": "tsconfig.spec.json", "assets": [{"glob": "**/*", "input": "public"}], "styles": ["src/styles.css"], "scripts": []}}}}}, "cli": {"analytics": false}}