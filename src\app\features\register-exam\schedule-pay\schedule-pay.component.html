<div class="container max-w-[1140px] mx-auto grid grid-cols-5 mt-5 gap-5">
    <div class="col-span-3">
        <div class="p-4 rounded-2xl bg-bg-white">
            <div class="font-semibold leading-6 mb-4">
                <span i18n="@@txt_personal_infor">Personal information</span>
                <span class="text-lg text-ct-error-primary">*</span>
            </div>
    
            <form class="grid grid-cols-2 gap-3">
                <div>
                    <label for="name" class="font-body standard-txt flex items-center gap-1 mb-1">
                        <span class="text-ct-primary text-sm leading-5" i18n="@@txt_exam_date">Exam Date</span>
                    </label>
                    <div class="select pr-[10px]"
                        [class.input-err]="submitted && idxRoundExam < 0"
                        [class.input-success]="submitted && idxRoundExam >= 0">
                        @if(roundExams) {
                            <select class="w-full" [(ngModel)]="idxRoundExam" name="roundExam" (ngModelChange)="ngOnChangeRound()">
                                @for (round of roundExams; track $index) {
                                    <option [ngValue]="$index">{{ round.exam_date }}</option>
                                }
                            </select>
                        }
                    </div>
                    <div class="flex items-center gap-1 mt-1">
                        @if(submitted && idxRoundExam < 0){
                            <app-svg-icon name="warning" fill="--ct-error-primary" [width]="16" [height]="16"></app-svg-icon>
                            <span class="text-[12px] text-ct-error-primary" i18n="@@txt_field_blank">This field cannot be blank</span>
                        }
                    </div>
                </div>
    
                <div>
                    <label for="name" class="font-body standard-txt flex items-center gap-1 mb-1">
                        <span class="text-ct-primary text-sm leading-5" i18n="@@txt_exam_time">Exam Time</span>
                    </label>
                    <div class="select pr-[10px]"
                        [class.input-err]="submitted && idxSlotExam < 0"
                        [class.input-success]="submitted && idxSlotExam >= 0">
                        <select class="w-full" [(ngModel)]="idxSlotExam" name="slotExam" (ngModelChange)="ngOnChangeSlot()">
                            @if(roundExams && roundExams[idxRoundExam]) {
                                @for (slot of roundExams[idxRoundExam].slots; track $index) {
                                    <option [ngValue]="$index">{{ slot.start_time }} - {{ slot.end_time }}</option>
                                }
                            }
                        </select>
                    </div>
                    <div class="flex items-center gap-1 mt-1">
                        @if(submitted && idxSlotExam < 0){
                            <app-svg-icon name="warning" fill="--ct-error-primary" [width]="16" [height]="16"></app-svg-icon>
                            <span class="text-[12px] text-ct-error-primary" i18n="@@txt_field_blank">This field cannot be blank</span>
                        }
                    </div>
                </div>
    
            </form>
    
            <hr class="border-1 border-dashed border-fg-tertiary my-4" />
    
            <ul class="list-disc pl-6 text-ct-secondary">
                <li>Thí sinh được phép thi nhiều kỳ thi trong <span>1 năm</span>, nhưng chỉ được thi <span>1 khung giờ/kỳ</span>.</li>
                <li>Mức phí cho bài thi: <span>1.000.000/JOPT - 40$</span> (có bản mềm chứng chỉ điểm online)</li>
                <li>Không hoàn hủy dưới mọi hình thức, thí sinh được phép thay đổi lịch thi <span>01 lần</span> mà không mất chi phí nào. Thời hạn chứng chỉ: <span>02 năm</span>.</li>
            </ul>
        </div>
        <div class="bg-bg-white p-4 rounded-2xl info-pack mt-3">
            <h2 class="text-lg font-semibold mt-0 mb-4" i18n="@@txt_select_pay">Select payment method</h2>
            <div class="flex items-center py-2 pointer" (click)="changeMethod(1, false)">
                <img width="20" src="/images/icons/ic_radio_test{{ !bankForeign && method ? '_checked' : '' }}.svg" alt="Radio">
                <img class="mx-2 w-[38px]" src="/images/icons/ic_atm.svg" alt="Pay icon">
                <span class="text-sm" i18n="@@txt_payment_bank">Payment by bank transfer</span>
            </div>
            <div class="flex items-center py-2 pointer" (click)="changeMethod(0, false)">
                <img width="20" src="/images/icons/ic_radio_test{{ method ? '' : '_checked' }}.svg" alt="Radio">
                <img class="mx-2 w-[38px]" src="/images/icons/ic_credit.svg" alt="Pay icon">
                <span class="text-sm" i18n="@@txt_pay_via_paypal">Pay via Paypal</span>
            </div>
        </div>
    </div>

    <div class="col-span-2">
        <div class="col-span-3 p-4 rounded-2xl bg-bg-white">
            <app-user-card />
        </div>
        <div class="p-4 rounded-2xl bg-bg-white mt-4">
            <h2 class="text-lg m-0 font-semibold mr-2 mb-4" i18n="@@txt_add_service">Additional services</h2>
            <div class="flex items-center justify-between py-2 pointer" (click)="changeMethod(1, false)">
                <div class="flex items-center gap-2">
                    <img width="20" src="/images/icons/ic_radio_test{{ !bankForeign && method ? '_checked' : '' }}.svg" alt="Radio">
                    <span class="text-sm" i18n="@@txt_certi_of_compet">Certificate of Competency</span>
                </div>
                <div class="font-semibold">20$</div>
            </div>
            <div class="flex items-center justify-between py-2 pointer" (click)="changeMethod(0, false)">
                <div class="flex items-center gap-2">
                    <img width="20" src="/images/icons/ic_radio_test{{ method ? '' : '_checked' }}.svg" alt="Radio">
                    <span class="text-sm" i18n="@@txt_certi_point">Certificate of points</span>
                </div>
                <div class="font-semibold">20$</div>
            </div>
        </div>
        <div class="p-4 rounded-2xl bg-bg-white mt-4">
            <h2 class="text-lg m-0 font-semibold mr-2 mb-4" i18n="">Tóm tắt hóa đơn</h2>
            <div class="flex items-center justify-between">
                <div class="text-ct-secondary" i18n="">Giá trị đơn hàng</div>
                <div class="font-semibold">39$</div>
            </div>
            <hr class="w-full border-t border-dashed border-t-fg-tertiary my-4"/>
            <div class="flex items-center justify-between">
                <div class="text-ct-secondary" i18n="">Tổng thanh toán</div>
                <div class="font-semibold">50$</div>
            </div>
        </div>
        <div class="mt-4" [class.hidden]="method > 0" #paypal></div>
        @if(packDetail && method) {
            <div class="btn-brand text-center text-sm mt-5 px-2 py-3 rounded-lg cursor-pointer" (click)="payBank()">
                <span i18n="@@txt_pay">Thanh toán</span>&nbsp;
                <span>{{ ((packDetail.sale ? packDetail.sale.prices[langPriceNew] : packDetail.prices[langPriceNew].price) * 0.95 | roundPrice:packDetail.prices[langPriceNew].currency) + ' ' + packDetail.prices[langPriceNew].symbol }}</span>
            </div>
        }
    </div>
</div>
<app-modal-register-success />