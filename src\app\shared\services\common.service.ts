import { afterNextR<PERSON>, ElementRef, Inject, Injectable, LOCALE_ID, signal, WritableSignal } from '@angular/core';
import { BroadcasterService } from './broadcaster.service';
import { AES , enc } from 'crypto-js';
import * as CONFIG from '../../common/constants/config';
import { HttpClient } from '@angular/common/http';
import { tap, of, map } from 'rxjs';
import { Router } from '@angular/router';
import { ModalService } from '../components/modal/modal.service';
import { ObjectKey, ResAdsinhouse, ResMessage } from '../../common/interfaces/common';
import { User, UserJoptInfo } from '../../common/interfaces/user';
import { ResCountryCode } from '../../common/interfaces/payment';

@Injectable({
  providedIn: 'root'
})
export class CommonService {

    svgCache: Map<string, string> = new Map();

    countryCode: string = '';
    environment: string = 'server';
    resAdsinhouse: ResAdsinhouse | undefined;

    sUser: WritableSignal<User | undefined> = signal(undefined)
    sUserJopt: WritableSignal<UserJoptInfo | undefined> = signal(undefined)

    constructor(
        private broadcaster: BroadcasterService,
        private modalService: ModalService,
        private http: HttpClient,
        @Inject(LOCALE_ID) private locale: string
    ) { 
        afterNextRender(() => {
            this.environment = 'client';
        });
    }

    get lang() {
        return this.locale;
    }

    get langToCountry() {
        if(this.locale === 'vi') {
            return 'vn';
        } else if (this.locale === 'zh') {
            return 'cn';
        } else if (this.locale === 'zh-Hant') {
            return 'tw'
        }
        return this.locale;
    }

    setLocal(key: string, value: any) {
        if (this.environment == 'client') {
            if (typeof value === 'string') {
                localStorage.setItem(key, value);
            } else {
                localStorage.setItem(key, JSON.stringify(value));
            }
        }
    }

    getLocal(key: string) {
        if (this.environment == 'client') {
            const value = localStorage.getItem(key);
            if (value) {
                try {
                    return JSON.parse(value);
                } catch (error) {
                    return value;
                }
            }
        }
        return '';
    }

    removeLocal(key: string) {
        if (this.environment == 'client') {
            localStorage.removeItem(key);
        }
    }

    setLocalDataImp(key: string, value: any) {
        const localData = AES.encrypt(JSON.stringify(value) + CONFIG.SECRET_KEY, CONFIG.SECRET_KEY).toString();
        this.setLocal(key, localData);
    }

    getLocalDataImp(key: string) {
        const localDataInit = this.getLocal(key);
        if (localDataInit) {
            const bytes = AES.decrypt(localDataInit, CONFIG.SECRET_KEY);
            const dataString = bytes.toString(enc.Utf8);
            return JSON.parse(dataString.slice(0, dataString.length - CONFIG.SECRET_KEY.length));
        } else {
            return undefined;
        }
    }

    getEnvironment() {
        return this.environment;
    }

    openModal(id: string) {
        this.modalService.open(id);
    }

    closeModal(id: string) {
        this.modalService.close(id);
    }

    closeAllModals() {
        this.modalService.closeAll();
    }

    scrollToTop() {
        if (this.getEnvironment() === 'client') {
            window.scroll({
                top: 0,
                left: 0,
                behavior: 'smooth'
            });
        }
    }

    showNotify(content: string, type: string, value?: ObjectKey<string | number>) {
        this.broadcaster.broadcast('notify', {
            'content': content,
            'type': type
        })
    }

    get resDataAdsinhouse() {
        return this.resAdsinhouse;
    }

    getAdsBanner(country: string) {
        const url = CONFIG.API_URL_SALE + `ads/adsInhouse?country=${country}&language=${this.locale}&platform=web&project_id=4`;
        if(this.resAdsinhouse) return of(this.resAdsinhouse);
        return this.http.get<ResAdsinhouse>(url, CONFIG.HTTP_OPTION).pipe(
            tap(res => this.resAdsinhouse = res)
        );
    }

    getCountryCode() {
        const url = CONFIG.BASE_API_URL + 'system/country-code';
        if(this.countryCode) return of(this.countryCode);
        return this.http.get<ResCountryCode>(url, CONFIG.HTTP_OPTION).pipe(
            map(res => {
                this.countryCode = res.countryCode;
                return res.countryCode;
            })
        );
    }

    getInfor() {
        const url = CONFIG.BASE_API_URL + 'jopt/user/info';
        return this.http.get<ResMessage<UserJoptInfo>>(url).pipe(
            tap(res => {
                this.sUserJopt.set(res.data ? res.data : undefined);
            })
        );
    }
}
