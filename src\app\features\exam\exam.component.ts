import { Component, OnInit, Output } from '@angular/core';
import { EventEmitter } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { DoExamComponent } from './do-exam/do-exam.component';
import { ActivatedRoute, Router } from '@angular/router';
import { TutorialComponent } from './tutorial/tutorial.component';
import { QuestionComponent } from './question/question.component';
import { ExamService } from './exam.service';
import { LoginCredentials } from '../../common/interfaces/exam';

@Component({
  selector: 'app-exam',
  standalone: true,
  imports: [CommonModule, FormsModule, DoExamComponent, TutorialComponent, QuestionComponent],
  templateUrl: './exam.component.html',
  styleUrl: './exam.component.css'
})
export class ExamComponent implements OnInit {
  examNo: number = 1;
  partExam: any = null;
  autoSwitch: any = false;
  currentStep: number = 0;
  remainingTime: number = 100;
  videoChunkIndex: number =1 ;
  constructor(private router: Router, private route: ActivatedRoute, private examService: ExamService) {
  }
  ngOnInit(): void {
    // Trong component
    this.examService.getExamData('ffb26cbb-65f6-4774-9b66-0e1b7e0cba43')
      .subscribe({
        next: (data) => {
          this.dataExam = data;
          console.log('Exam data:', data)
        },
        error: (err) => console.error('Error:', err)
      });
  }
  dataExam: any;
  step: any = 1;
  mainData: any = {};
  onNextStep(data: any) {
    if (this.currentStep == 1) { // Kiểm tra thiết bị
      this.mainData.step1 = data;
      this.currentStep = 2;
    } else if (this.currentStep == 2) { // Hướng dẫn làm bài thi
      this.mainData.step2 = data;
      this.currentStep = 3;
      this.examNo = this.mainData && this.mainData.step3 && this.mainData.step3.examNo ? this.mainData.step3.examNo : 1;
    } else if (this.currentStep == 3) { 
      this.videoChunkIndex = data.chuckIndex; // Làm bài thi
      this.mainData.step3 = data;
      this.examNo = data.examNo;
      this.partExam = data.currentGroupIndexLevel2 + 1;
      this.remainingTime = data.timeRemaning;
      this.currentStep = 2;
      console.log('Thi xong r :', this.remainingTime);
    }
    console.log('Data from child component:', this.mainData);
  }
  // báo bug
  showPopup = false;
  selectedOption: string = '';
  options = [
    'Lỗi không nhận mic',
    'Lỗi không nhận video',
    'Lỗi màn hình đen',
    'Lỗi không nghe thấy tiếng',
    'Khác'
  ];

  submitError() {
    console.log('Báo lỗi:', this.selectedOption);
    this.showPopup = false;
  }
  // setting
  showSettingPopup = false;
  fontSizes = [12, 14, 16, 18, 20];
  selectedFontSize = 14;

  toggleSettingPopup() {
    this.showSettingPopup = !this.showSettingPopup;
  }

  setFontSize(size: number) {
    this.selectedFontSize = size;
    document.documentElement.style.fontSize = `${size}px`;
    this.showSettingPopup = false;
  }

  autoSwitchChange() {
    this.autoSwitch = !this.autoSwitch;
    this.mainData.autoSwitch = this.autoSwitch;
    console.log('Auto switch changed:', this.autoSwitch);
  }

  // login

  @Output() loginSubmit = new EventEmitter<LoginCredentials>();

  studentId: string = '';
  password: string = '';
  isPasswordVisible: boolean = false;


  togglePasswordVisibility(): void {
    this.isPasswordVisible = !this.isPasswordVisible;
  }

  onSubmit(): void {
    if (this.studentId.trim() && this.password.trim()) {
      const credentials: LoginCredentials = {
        studentId: this.studentId.trim(),
        password: this.password.trim()
      };
       this.examService.register(credentials).subscribe({
        next: (response) => {
          console.log('Đăng nhập thành công:', response);
          // Xử lý khi đăng nhập thành công (ví dụ: lưu token, chuyển trang...)
        },
        error: (err) => {
          console.error('Lỗi đăng nhập:', err);
          // Hiển thị thông báo lỗi cho người dùng
        }
      });
    }
    this.currentStep = 1 ;
  }

  ngAfterViewInit(): void {
    // Focus the first input field when modal opens
    const firstInput = document.getElementById('student-id');
    if (firstInput) {
      firstInput.focus();
    }
  }
}
