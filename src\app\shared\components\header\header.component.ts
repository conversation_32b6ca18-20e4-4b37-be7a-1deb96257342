import { afterNextRender, Component, DestroyRef, Input } from '@angular/core';
import { NavigationEnd, NavigationStart, Router, RouterModule } from '@angular/router';
import { BroadcasterService } from '../../services/broadcaster.service';
import { CommonService } from '../../services/common.service';
import { UserService } from '../../services/user.service';
import { LANGUAGES } from '../../../common/constants/header';
import { UserJoptInfo } from '../../../common/interfaces/user';
import { NgClass } from '@angular/common';
import { SvgIconComponent } from "../svg-icon/svg-icon.component";
import { CommonModule } from '@angular/common';
@Component({
    selector: 'app-header',
    standalone: true,
    imports: [
        RouterModule,
        NgClass,
        SvgIconComponent,
        CommonModule
    ],
    templateUrl: './header.component.html',
    styleUrls: [
        './header.component.css',
        '../../styles/button.css',
        '../../styles/badge.css',
    ]
})
export class HeaderComponent {
    constructor(
        private router: Router,
        protected readonly commonService: CommonService,
        private userService: UserService,
        private broadcaster: BroadcasterService,
    ) {
        afterNextRender(() => {
            this.userService.initLogin();
        })
    }

    user: UserJoptInfo | undefined;

    selectedLang = LANGUAGES[0];
    languages = LANGUAGES;
    language: string = 'vi';
    // path: string = '/';
    currentUrl: string = '';
    @Input() isExam: boolean = false;
    ngOnInit(): void {
        this.currentUrl = this.router.url;
        this.language = this.commonService.lang;
        const langSelect = this.languages.find(l => l.code === this.language);
        if (langSelect) {
            this.selectedLang = langSelect;
        }
        this.router.events.subscribe(event => {
            if (event instanceof NavigationEnd) {
                this.currentUrl = event.url;
            }
        });
    }

    isActive(path: string) {
        return this.currentUrl.includes(path);
    }

    logout() {
        this.userService.logout().subscribe({
            next: (res) => {
                const message = $localize`:@@logout_success:Logout successfully`;
                this.commonService.showNotify(message, 'success');
                this.userService.clearInforUser();
                this.router.navigate(['/']);
            },
            error: (err) => {
                this.userService.clearInforUser();
            }
        });
    }

    openAuth() {
        this.broadcaster.broadcast('auth');
    }
}
