.select{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 10px 12px;
    background-color: var(--bg-white);
    font-size: 14px;
    line-height: 20px;
    font-weight: 400;
    border-radius: 8px;
    border: 1px solid var(--bg-secondary)
}

.select:focus{
    border: 1px solid var(--bd-brand);
    box-shadow: 0px 0px 0px 2px #FFF, 0px 0px 0px 4px var(--color-teal-5);
}

.select-child{
    display: flex !important;
    flex-direction: column;
    border-radius:  8px;
    border: 1px solid var(--bd-secondary);
    background: var(--bg-white);
    box-shadow: 0px 0px 11px 0px rgba(128, 131, 141, 0.17);
}

.child-selected{
    color: var(--ct-brand-primary);
    background: var(--bg-brand-secondary);
}