import { Component, OnInit, ViewEncapsulation, Input, ElementRef, ViewChild } from '@angular/core';

import { ModalService } from './modal.service';
import { CommonService } from '../../services/common.service';

@Component({
    selector: 'app-modal',
    templateUrl: './modal.component.html',
    standalone: true,
    imports: [],
    styleUrls: ['./modal.component.css'],
    encapsulation: ViewEncapsulation.None
})
export class ModalComponent implements OnInit {
    @Input() id!: string;
    @Input() clickOutClose: boolean = true;

    private element: any;
    listTop2: string[] = ['modal-register-exam-success'];

    @ViewChild('modalElm') modalElm!: ElementRef;

    constructor(
        private modalService: ModalService,
        private commonService: CommonService,
        private el: ElementRef
    ) {
        this.element = el.nativeElement;
    }

    ngOnInit(): void {
        // ensure id attribute exists
        if (!this.id) {
            return;
        }

        // close modal on background click
        this.element.addEventListener('click', (el: any) => {
            if (el.target.className.includes('app-modal') && this.clickOutClose) {
                this.close();
            }
        });

        // add self (this modal instance) to the modal service so it's accessible from controllers
        this.modalService.add(this);
    }

    // remove self from modal service when component is destroyed
    ngOnDestroy(): void {
        this.modalService.remove(this.id);
        this.element.remove();
    }

    // open modal
    open(): void {
        this.element.style.display = 'block';
        if (this.commonService.getEnvironment() == 'client') {
            document.body.appendChild(this.element);
            //add style
            document.body.classList.add('app-modal-open');
        }
    }

    // close modal
    close(): void {
        this.element.style.display = 'none';
        if (this.commonService.getEnvironment() == 'client') {
            document.body.classList.remove('app-modal-open');
        }
    }

    closeAll() {
        if (this.commonService.getEnvironment() == 'client') {
            document.body.classList.remove('app-modal-open');
        }
    }
}
