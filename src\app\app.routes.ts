import { Routes } from '@angular/router';
import { scheduleRoutes } from './features/register-exam/schedule-pay/schedule-pay.routes';

export const routes: Routes = [
    {
        path: 'schedule',
        children: scheduleRoutes
    },
    {
        path: '',
        loadComponent: () => import('./features/register-exam/register-exam.component').then(m => m.RegisterExamComponent),
    },
  {
    path: 'exam',
    children: [
      {
        path: '',
        loadComponent: () =>
          import('./features/exam/exam.component').then(m => m.ExamComponent)
      },
      {
        path: 'doExam',
        loadComponent: () =>
          import('./features/exam/do-exam/do-exam.component').then(m => m.DoExamComponent)
      }
    ]
  }
];
