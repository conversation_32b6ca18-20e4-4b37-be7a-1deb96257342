<app-modal id="modal-require-image-id">
    <div class="py-5 pl-5 pr-3 bg-bg-white rounded-2xl w-max max-w-[640px] fixed -translate-x-2/4 -translate-y-2/4 left-2/4 top-2/4">
        <h2 class="text-lg font-medium mb-5" i18n="@@txt_title_require_image_and_documents">
            Photo and Document Requirements
        </h2>
        <div class="max-h-[70vh] overflow-auto scrollbar-common pr-2 text-sm">
            <div class="section">
                <h3 class="font-semibold mb-3" i18n="@@txt_title_id_photo_requirements">ID Photo Requirements</h3>
    
                <div class="mb-2 mt-3" i18n="@@txt_photo_size_title">1. Digital Photo Dimensions</div>
                <ul class="list-disc pl-6">
                    <li i18n="@@txt_photo_size">Printed size: 4 cm × 6 cm</li>
                    <li i18n="@@txt_photo_format">File format: JPG/JPEG/PNG</li>
                    <li i18n="@@txt_photo_max_size">File size: Under 2MB</li>
                </ul>
    
                <div class="mb-2 mt-3" i18n="@@txt_photo_content_requirements_title">2. Content Requirements</div>
                <ul class="list-disc pl-6">
                    <li i18n="@@txt_photo_background">Background: plain, no patterns or shadows (white background recommended)
                    </li>
                    <li i18n="@@txt_photo_face">Face: looking straight, serious expression, both ears and eyes visible</li>
                    <li i18n="@@txt_photo_clothing">Clothing: formal</li>
                    <li i18n="@@txt_photo_no_accessories">No glasses or accessories such as sunglasses, headphones, hats, or
                        masks</li>
                    <li i18n="@@txt_photo_lighting">Lighting: even, no shadows, glare, or color distortion</li>
                    <li i18n="@@txt_photo_framing">Framing: should show both shoulders, face occupies about 70–80% of photo
                        height</li>
                    <li i18n="@@txt_photo_recent">Photo must be taken within the last 6 months</li>
                </ul>
    
                <div class="mb-2 mt-3" i18n="@@txt_photo_invalid_cases_title">3. Invalid Cases:</div>
                <ul class="list-disc pl-6">
                    <li i18n="@@txt_photo_selfie">Selfies or cropped group photos</li>
                    <li i18n="@@txt_photo_heavy_filters">Photos with excessive filters making recognition difficult</li>
                    <li i18n="@@txt_photo_blur">Blurred or pixelated images</li>
                    <li i18n="@@txt_photo_wrong_ratio">Wrong aspect ratio or oversized files</li>
                    <li i18n="@@txt_photo_bad_background">Improper background, objects or people in background</li>
                </ul>
            </div>
    
            <div class="section">
                <h3 class="font-semibold mb-3 mt-4" i18n="@@txt_title_cccd_requirements">National ID (CCCD) Requirements</h3>
                <div class="mb-2 mt-3" i18n="@@txt_cccd_front_title">1. Clear Front Side</div>
                <ul class="list-disc pl-6">
                    <li i18n="@@txt_cccd_front_content">Must show front of CCCD (photo, full name, ID number...)</li>
                    <li i18n="@@txt_cccd_front_all_corners">All 4 corners visible, no cropping</li>
                    <li i18n="@@txt_cccd_front_clear">No blur, glare, or smudged text</li>
                    <li i18n="@@txt_cccd_no_filter">No filter, beautification, or editing</li>
                </ul>
    
                <div class="mb-2 mt-3" i18n="@@txt_cccd_back_title">2. Back Side</div>
                <ul class="list-disc pl-6">
                    <li i18n="@@txt_cccd_back_content">Photo of the back with visible chip and QR code</li>
                </ul>
    
                <div i18n="@@txt_cccd_lighting_title">3. Lighting and Background</div>
                <ul class="list-disc pl-6">
                    <li i18n="@@txt_cccd_lighting">Take in natural light, avoid darkness or yellow light</li>
                    <li i18n="@@txt_cccd_background">Place CCCD on plain surface with no patterns or objects</li>
                </ul>
                <div class="mb-2 mt-3" i18n="@@txt_cccd_format_title">4. Format and Size</div>
                <ul class="list-disc pl-6">
                    <li i18n="@@txt_cccd_file_format">File format: JPG, JPEG, or PNG</li>
                    <li i18n="@@txt_cccd_file_size">File size: under 5MB</li>
                    <li i18n="@@txt_cccd_no_damage">No blur, rotated, or cropped edges</li>
                </ul>
            </div>
            <div class="section">
                <h3 class="font-semibold mb-3 mt-4" i18n="@@txt_title_passport_requirements">Passport Requirements</h3>
                <div class="mb-2 mt-3" i18n="@@txt_passport_info_title">1. Passport Information Page</div>
                <ul class="list-disc pl-6">
                    <li i18n="@@txt_passport_required_page">Must include portrait, full name, passport number, and nationality</li>
                    <li i18n="@@txt_passport_full_corners">All 4 corners visible, no cropping</li>
                    <li i18n="@@txt_passport_clear_text">No blur, glare, or smudged text</li>
                    <li i18n="@@txt_passport_no_editing">No filters, editing, or beautification</li>
                    <li i18n="@@txt_passport_lighting">Take in natural or soft white light</li>
                </ul>
                <div class="mb-2 mt-3" i18n="@@txt_passport_format_title">2. Format and Size</div>
                <ul class="list-disc pl-6">
                    <li i18n="@@txt_passport_file_format">File format: JPG, JPEG, or PNG</li>
                    <li i18n="@@txt_passport_file_size">File size: under 5MB</li>
                </ul>
                <div class="mb-2 mt-3" i18n="@@txt_passport_validity_title">3. Other Requirements</div>
                <ul class="list-disc pl-6">
                    <li i18n="@@txt_passport_validity">Passport must be valid</li>
                </ul>
            </div>
        </div>
        <div class="flex justify-end" (click)="commonService.closeModal('modal-require-image-id')">
            <div class="h-10 w-1/2 text-ct-white text-sm pointer flex items-center justify-center rounded-lg bg-bg-brand-solid" i18n="@@txt_confirm">Confirm</div>
        </div>
        <div class="p-[5px] pointer rounded-lg absolute right-4 top-4 bg-[#0a0f290a]" (click)="commonService.closeModal('modal-require-image-id')">
            <img src="/images/icons/ic_close_modal.svg" alt="Close">
        </div>
    </div>
</app-modal>