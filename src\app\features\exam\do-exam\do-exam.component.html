<div class="device-test-container">
  <div class="page-content">
    <div class="content-container">
      <div class="camera-section">
        <div class="camera-container">
          <!-- Camera Video Display -->
          <div class="camera-video-container">
            <video #videoElement class="camera-video" [class.hidden]="!isCameraActive" autoplay muted playsinline>
            </video>

            <!-- Camera Placeholder when not active -->
            <div class="camera-placeholder" [class.hidden]="isCameraActive">
              <div class="placeholder-content">
                <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"
                  class="camera-placeholder-icon">
                  <path
                    d="M8 14C8 11.7909 9.79086 10 12 10H16L18 6H30L32 10H36C38.2091 10 40 11.7909 40 14V34C40 36.2091 38.2091 38 36 38H12C9.79086 38 8 36.2091 8 34V14Z"
                    stroke="#9CA3AF" stroke-width="2" />
                  <circle cx="24" cy="24" r="6" stroke="#9CA3AF" stroke-width="2" />
                </svg>
                <div class="placeholder-text">Camera chưa được bật</div>
              </div>
            </div>

            <!-- Camera Loading State -->
            <div class="camera-loading" [class.hidden]="!isCameraLoading">
              <div class="loading-spinner"></div>
              <div class="loading-text">Đang khởi động camera...</div>
            </div>

            <!-- Camera Error State -->
            <div class="camera-error" [class.hidden]="!cameraError">
              <div class="error-icon">⚠️</div>
              <div class="error-text">{{cameraError}}</div>
            </div>
          </div>

          <!-- Camera Controls -->
          <div class="camera-controls">
            <div class="camera-info">
              <div class="alert-content">
                <div class="alert-icon-container">
                  <img src="/images/icons/camera-02.svg" alt="Camera info" width="16" height="16" class="info-icon">
                </div>

                <div class="alert-text">
                  <div class="alert-message">Hệ thống sẽ ghi lại hình ảnh quá trình thí sinh thực hiện bài thi.</div>
                </div>
              </div>

              <div class="camera-toggle-container">
                <!-- Camera Selection Dropdown -->
                <div class="camera-select" *ngIf="availableCameras.length > 1">
                  <select (change)="switchCamera($event)" [value]="selectedCamera" class="camera-dropdown">
                    <option *ngFor="let camera of availableCameras" [value]="camera.deviceId">
                      {{camera.label || 'Camera ' + (availableCameras.indexOf(camera) + 1)}}
                    </option>
                  </select>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="controls-section">
        <div class="controls-container">
          <!-- Output Device Section -->
          <div class="device-section">
            <div class="section-title">
              <img src="/images/header/computer-audio.png" alt="Output Device" width="24" height="24"
                class="audio-icon">
              <div class="title-text">Thiết bị đầu ra</div>
              <button style="text-align: right;" class="title-text" (click)="ishaveTroble(false) ">Hướng dẫn cài đặt
              </button>
              <img src="\images\header\logohoichammanhd.svg" alt="Output Device" width="24" height="24"
                class="audio-icon">
            </div>
            <div class="device-dropdown">
              <select class="dropdown-select" (change)="onOutputDeviceChange($event)" [value]="selectedOutputDevice">
                <option value="" disabled>Chọn thiết bị đầu ra</option>
                <option *ngFor="let device of availableOutputDevices" [value]="device.deviceId">
                  {{getDeviceLabel(device)}}
                </option>
              </select>
            </div>

            <div class="volume-section">
              <div class="volume-label">Âm lượng đầu ra</div>

              <div class="volume-control">
                <img src="/images/header/Volume-high.png" alt="Volume" width="24" height="24" class="volume-icon">

                <div class="volume-slider-container">
                  <div class="volume-value">{{outputVolume}}</div>

                  <div class="volume-slider-wrapper">
                    <input type="range" min="0" max="100" [value]="outputVolume" (input)="onOutputVolumeChange($event)"
                      class="volume-slider" />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- Input Device Section -->
          <div class="device-section2">
            <div class="section-title">
              <img src="/images/header/mic-01.png" alt="Microphone" width="24" height="24" class="mic-icon-gray">
              <div class="title-text">Thiết bị đầu vào</div>
              <button style="text-align: right;" class="title-text" (click)="ishaveTroble(true) ">Hướng dẫn cài đặt
              </button>
              <img src="\images\header\logohoichammanhd.svg" alt="Output Device" width="24" height="24"
                class="audio-icon">
            </div>

            <div class="device-dropdown">
              <select class="dropdown-select" (change)="onInputDeviceChange($event)" [value]="selectedInputDevice">
                <option value="" disabled>Chọn thiết bị đầu vào</option>
                <option *ngFor="let device of availableInputDevices" [value]="device.deviceId">
                  {{getDeviceLabel(device)}}
                </option>
              </select>
            </div>

            <div class="volume-section">
              <div class="volume-label">Âm lượng đầu vào</div>

              <div class="volume-control">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"
                  class="mic-icon-gray">
                  <path
                    d="M17 7V11C17 13.7614 14.7614 16 12 16C9.23858 16 7 13.7614 7 11V7C7 4.23858 9.23858 2 12 2C14.7614 2 17 4.23858 17 7Z"
                    stroke="#1C2024" stroke-width="1.5"></path>
                  <path d="M17 7H14M17 11H14" stroke="#1C2024" stroke-width="1.5" stroke-linecap="round"></path>
                  <path d="M20 11C20 15.4183 16.4183 19 12 19M12 19C7.58172 19 4 15.4183 4 11M12 19V22M12 22H15M12 22H9"
                    stroke="#1C2024" stroke-width="1.5" stroke-linecap="round"></path>
                </svg>

                <div class="volume-slider-container">
                  <div class="volume-value">{{inputVolume}}</div>

                  <div class="volume-slider-wrapper">
                    <input type="range" min="0" max="100" [value]="inputVolume" (input)="onInputVolumeChange($event)"
                      class="volume-slider" />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- Audio Test Section -->
          <div class="audio-test-section">
            <div class="test-info">
              <div class="test-title">Kiểm tra mic và tai nghe</div>
              <div class="test-description">Kiểm tra mic và tai nghe của bạn bằng cách nói gì đó và chúng tôi sẽ phản
                hồi lại.</div>
            </div>
            <div class="test-controls">
              <button class="test-button" (click)="startAudioTest()"
                [disabled]="!selectedInputDevice || !selectedOutputDevice">
                <div class="button-text">{{isAudioTesting ? 'Đang kiểm tra' : 'Để xem nào'}}</div>
              </button>

              <div class="audio-visualizer">
                <div class="audio-bar" *ngFor="let bar of audioVisualizationBars; let i = index"
                  [style.height.%]="bar * 100" [class.active]="bar > 0"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="action-buttons">
      <button class="back-button" (click)="goBack()">
        <div class="button-text">Quay lại</div>
      </button>

      <button class="confirm-button" (click)="confirmDevices()">
        <div class="button-text">Xác nhận thiết bị và tiếp tục</div>
      </button>
    </div>
  </div>

  <!-- Popup 1 -->
  <div class="popup-overlay-trobel" *ngIf="showPopupTroble">
    <div class="popup-box-trobel">
      <div class="popup-header-trobel">
        <h3>Các cách khắc phục lỗi không nhận <span *ngIf="isMic">mic</span><span *ngIf="!isMic">tai nghe</span></h3>
        <button class="close-btn-trobel" (click)="showPopupTroble = false">×</button>
      </div>
      <div *ngIf="!isMic" class="popup-body-trobel">
        <p><b>1. Kiểm tra xem tai nghe có bị hỏng không?</b></p>
        <p class="text-infor-trob">Việc đầu tiên để khắc phục lỗi máy tính không nhận tai nghe là kiểm tra tai nghe
          có bị hỏng không. Bạn
          có thể sử dụng tai nghe khác cắm vào laptop, hoặc thử cắm tai nghe đang sử dụng vào thiết bị khác để xác định
          xem nguyên nhân là do tai nghe hay do máy.
          Nếu sử dụng tai nghe khác mà laptop nhận bình thường, thì tai nghe của bạn đã hỏng. Hãy thay thế chiếc tai
          nghe khác để sử dụng nhé!</p>
        <img class="camera-view-trobel" src="\images\header\image 225.png" alt="Avatar" />
        <p><b>2. Khởi động lại máy tính</b></p>
        <p class="text-btn-trobel"> Trên máy tính, thỉnh thoảng sẽ xảy ra việc xung đột giữa các phần mềm khiến việc
          truyền dẫn âm thanh gặp trục trặc. Chính vì vậy, hãy thử khởi động lại máy tính và cắm tai nghe vào lại, kiểm
          tra xem có nghe được không nhé!
          Để khởi động lại laptop, bạn nhấn chọn <Strong> biểu tượng Windows </Strong>> Chọn <Strong> Power</Strong> >
          <Strong> Restart.</Strong>
        </p>
        <img class="camera-view-trobel" src="\images\header\image 226.png" alt="Avatar" />

        <p><b>3. Kiểm tra phần cài đặt âm thanh</b></p>
        <p class="text-btn-trobel"> Trong cài đặt âm thanh, bạn cần kiểm tra 2 mục đó là Volume Mixer và Device Usage để
          xem liệu có mục nào đang bị tắt âm hay không.

          <Strong>- Kiểm tra Volume Mixer</Strong>
        </p>

        <p class="text-btn-trobel">"><Strong>Bước 1:</Strong> Click chuột phải vào biểu tượng loa trên
          <Strong>Taskbar</Strong> > Chọn <Strong>
            Open Volume
            Mixer
          </Strong>.
        </p>
        <img class="camera-view-trobel" src="\images\header\image 227.png" alt="Avatar" />

        <p class="text-btn-trobel">
          <Strong>Bước 2:</Strong> Quan sát 2 mục <Strong>Device</Strong> và <Strong>Applications</Strong> để
          kiểm tra xem thiết bị có ở trạng thái tắt âm thanh hay không. Nếu có hãy điều chỉnh lại thành chế độ bật âm
          thanh bằng cách nhấn vào biểu tượng loa.
        </p>
        <img class="camera-view-trobel" src="\images\header\image 228.png" alt="Avatar" />
        <img class="camera-view-trobel" src="\images\header\image 229.png" alt="Avatar" />
        <p class="text-btn-trobel"> <strong>- Kiểm tra Device Usage</strong></p>
        <p class="text-btn-trobel"><Strong>Bước 1</Strong>: Click chuột phải vào biểu tượng loa trên
          <Strong>Taskbar</Strong> > Chọn
          <Strong>Sound</Strong>.
          <img class="camera-view-trobel" src="\images\header\image 230.png" alt="Avatar" />
        </p>
        <p class="text-btn-trobel"><Strong>Bước 2:</Strong> Chọn <Strong>Playback</Strong>, bạn click chuột phải vào
          <Strong>Headphones</Strong> > Chọn
          <Strong>Properties</Strong>.
          <img class="camera-view-trobel" src="\images\header\image 231.png" alt="Avatar" />
        </p>
        <p class="text-btn-trobel"><Strong>Bước 3:</Strong> Tại mục <Strong>Device usage</Strong> > Chọn <Strong>Use
            this device (enable)</Strong> >
          Click chọn <Strong>OK</Strong>.
          <img class="camera-view-trobel" src="\images\header\image 232.png" alt="Avatar" />
        </p>

        <p><b>4. Sử dụng tính năng khắc phục sự cố âm thanh tích hợp</b></p>
        <p class="text-btn-trobel"><Strong>Bước 1:</Strong> Tại cửa sổ <Strong>Windows</Strong>, gõ tìm kiếm
          <Strong>Troubleshoot</Strong> > Mở <Strong>
            Troubleshoot Settings
          </Strong>.
          <img class="camera-view-trobel" src="\images\header\image 233.png" alt="Avatar" />
        </p>
        <p class="text-btn-trobel">
          <Strong>Bước 2</Strong>: Click chọn <Strong>Additional troubleshooters</Strong>.
          <img class="camera-view-trobel" src="\images\header\image 234.png" alt="Avatar" />
        </p>
        <p class="text-btn-trobel">
          <Strong>Bước 3</Strong>: Click chọn <Strong>Playing audio</Strong> > Chọn <Strong>Run the
            troubleshooter</Strong>.
          <img class="camera-view-trobel" src="\images\header\image 235.png" alt="Avatar" />
        </p>
        <p class="text-btn-trobel">
          <Strong>Bước 4</Strong>: Click chọn <Strong>Headphones</Strong> > Chọn <Strong>Next</Strong>.
          <img class="camera-view-trobel" src="\images\header\image 236.png" alt="Avatar" />
        </p>
        <p class="text-btn-trobel">
          <Strong>Bước 5</Strong>: Click chọn <Strong>No, Do not open Audio Enhancements.</Strong>.
          <img class="camera-view-trobel" src="\images\header\image 237.png" alt="Avatar" />
        </p>
        <p class="text-btn-trobel">
          <Strong>Bước 6</Strong>: Click chọn <Strong>Play test sounds</Strong>.
          <img class="camera-view-trobel" src="\images\header\image 238.png" alt="Avatar" />
        </p>
        <p class="text-btn-trobel">Sau khi thực hiện, nếu bạn vẫn không nghe tiếng phát ra từ tai nghe, hãy chọn
          <Strong>
            I
            didn't hear anything
          </Strong>.
          Sau đó máy tính sẽ tự tìm và khắc phục lỗi.
          <img class="camera-view-trobel" src="\images\header\image 239.png" alt="Avatar" />
        </p>

        <p><b>5. Cài đặt lại driver âm thanh cho máy</b></p>
        <p class="text-btn-trobel"><Strong>Driver</Strong> là phần mềm quan trọng hỗ trợ người dùng sử dụng các thiết bị
          ngoại vi như loa, chuột, bàn phím,... Rất có thể driver âm thanh trên máy tính của bạn đã gặp vấn đề, cần phải
          cài đặt lại
          để có thể sử dụng.
        </p>
        <p class="text-btn-trobel">
          <Strong>Bước 1</Strong>: Nhấn tổ hợp phím <Strong>Windows + R</Strong> > Nhập <Strong>devmgmt.msc</Strong> >
          Nhấn <Strong>OK.</Strong>
          <img class="camera-view-trobel" src="\images\header\image 240.png" alt="Avatar" />
        </p>
        <p class="text-btn-trobel">
          <Strong>Bước 2:</Strong> Tại biểu tượng <Strong>Device Manager</Strong>, click đúp chuột vào mục
          <Strong>Sound, video and game controllers</Strong> (hoặc click
          vào dấu mũi tên bên cạnh).
          <img class="camera-view-trobel" src="\images\header\image 241.png" alt="Avatar" />
        </p>
        <p class="text-btn-trobel">
          <Strong>Bước 3:</Strong> Click chuột phải vào <Strong>RealTek (R) Audio</Strong> > Chọn <Strong>Uninstall
            device.</Strong>
          <img class="camera-view-trobel" src="\images\header\image 242.png" alt="Avatar" />
        </p>
        <p class="text-btn-trobel">
          <Strong>Bước 4:</Strong> Nhấn chuột phải vào mục <Strong>Sound, video and game controllers</Strong> > Chọn
          <Strong>
            Scan for hardware
            changes.
          </Strong>
          <img class="camera-view-trobel" src="\images\header\image 243.png" alt="Avatar" />
        </p>
        <p><b>6. Kiểm tra và cập nhật driver âm thanh</b></p>
        <p class="text-btn-trobel">Kiểm tra cập nhật driver âm thanh nhằm khắc phục tình trạng driver xung đột với các
          ứng dụng trong máy tính, hoặc phiên bản driver quá cũ không còn tương thích với hệ điều hành. Để khắc phục
          điều này, cập nhật driver âm thanh bằng cách:
        </p>
        <p class="text-btn-trobel">
          <Strong>Bước 1:</Strong> Nhấn tổ hợp phím <Strong>Windows + R</Strong> > Nhập <Strong>devmgmt.msc</Strong> >
          Nhấn <Strong>OK</Strong>.
          <img class="camera-view-trobel" src="\images\header\image 245.png" alt="Avatar" />
        </p>
        <p class="text-btn-trobel">
          <Strong>Bước 2:</Strong> Tại biểu tượng <Strong>Device Manager</Strong>, click chọn <Strong>Sound,
            video and game controllers.</Strong>
          <img class="camera-view-trobel" src="\images\header\image 246.png" alt="Avatar" />
        </p>
        <p class="text-btn-trobel">
          <Strong>Bước 3:</Strong> Click chuột phải vào <Strong>RealTek (R) Audio</Strong> > Chọn <Strong>Update
            driver.</Strong>
          <img class="camera-view-trobel" src="\images\header\image 247.png" alt="Avatar" />
        </p>
        <p class="text-btn-trobel">
          <Strong>Bước 4</Strong>: <Strong>Update driver</Strong>:
          Để tìm driver cập nhật tự động cho máy, chọn <Strong>Search automatically for updated driver
            software.</Strong>
          Để cập nhật driver có sẵn mà bạn đã tải về máy, chọn Browse my computer for driver software.
          <img class="camera-view-trobel" src="\images\header\image 248.png" alt="Avatar" />
        </p>
      </div>
      <div *ngIf="isMic" class="popup-body-trobel">
        <p><b>1. Hướng dẫn bật mic laptop và máy tính</b></p>
        <p class="text-infor-trob"><Strong>- Bật micro từ Sound Settings</Strong></p>
        <p class="text-infor-trob"><Strong>Bước 1</Strong>: Nhấn tổ hợp phím <Strong>Windows + I</Strong> trên bàn
          phím máy tính.</p>
        <p class="text-infor-trob"><Strong>Bước 2: </Strong>Tại cửa sổ <Strong>System</Strong> tìm chọn mục
          <Strong>Sound</Strong> để
          mở cửa sổ cài đặt âm thanh
          <img class="camera-view-trobel" src="\images\header\image 225.png" alt="Avatar" />
        </p>
        <p class="text-infor-trob"><Strong>Bước 3:</Strong> Nhấp vào biểu tượng mở cửa sổ ứng dụng tại mục <Strong>
            More sound
            settings.
          </Strong>
          <img class="camera-view-trobel" src="\images\header\image 249.png" alt="Avatar" />
        </p>
        <p class="text-infor-trob">Bước 4: Lúc này máy tính của bạn sẽ hiện ra cửa sổ <Strong>Sound</Strong>, tại đây
          bạn click vào
          phần <Strong>Recording</Strong> và nhấp chuột phải vào biểu tượng thiết bị mic cần chỉnh.
          <img class="camera-view-trobel" src="\images\header\image 250.png" alt="Avatar" />
        </p>
        <p class="text-infor-trob"><Strong>Bước 5:</Strong> Chọn <Strong>Enable</Strong> để bật mic.
          <img class="camera-view-trobel" src="\images\header\image 251.png" alt="Avatar" />
        </p>
        <p class="text-infor-trob"><Strong>Cách khác:</Strong> Chọn biểu tượng thiết bị mic cần chỉnh rồi click vào ô
          lệnh <Strong>Properties</Strong>.
          <img class="camera-view-trobel" src="\images\header\image 252.png" alt="Avatar" />
        </p>
        <p class="text-infor-trob">Tại cửa sổ <Strong>General</Strong>, tìm <Strong>Device usage</Strong> sau đó chọn
          vào mục <Strong>Use this device (enable).</Strong>
          <img class="camera-view-trobel" src="\images\header\image 253.png" alt="Avatar" />
        </p>
        <p class="text-infor-trob"><Strong>Bước 6:</Strong> Nhấn <Strong>OK</Strong> để hoàn thành.

        </p>
        <p><b>2. Bật micro từ Windows Settings</b></p>
        <p class="text-btn-trobel"> <Strong>Bước 1:</Strong>Nhấn chọn vào biểu tượng âm thanh trên
          <Strong>Taskbar</Strong>, sau đó chọn
          <Strong>Sound settings.</Strong>
          <img class="camera-view-trobel" src="\images\header\image 254.png" alt="Avatar" />
        </p>
        <p class="text-btn-trobel"><Strong>Bước 2:</Strong> Tại cửa sổ <Strong>Sound settings</Strong> tìm chọn
          <Strong>Microphone</Strong>.
          <img class="camera-view-trobel" src="\images\header\image 255.png" alt="Avatar" />
        </p>
        <p class="text-btn-trobel">
          <Strong>Bước 3:</Strong> Tại mục <Strong>Audio</Strong> chọn <Strong>Allow</Strong> để cho phép máy tính của
          bạn truy cập vào <Strong>Micro</Strong>.
          <img class="camera-view-trobel" src="\images\header\image 256.png" alt="Avatar" />
        </p>

        <p><b>3. Bật micro từ Device Manager</b></p>
        <p class="text-btn-trobel"><Strong>Bước 1:</Strong> Nhấn tổ hợp phím<Strong> Windows + X</Strong> trên bàn phím.
        </p>
        <p class="text-btn-trobel">Bước 2: Tìm chọn <Strong>Device Manager.</Strong>
          <img class="camera-view-trobel" src="\images\header\image 257.png" alt="Avatar" />
        </p>
        <p class="text-btn-trobel">
          <Strong>Bước 3:</Strong> Click mở rộng phần <Strong>Sound Video and Game Controllers.</Strong>
          <img class="camera-view-trobel" src="\images\header\image 258.png" alt="Avatar" />
        </p>
        <p class="text-btn-trobel">
          <Strong>Bước 4:</Strong> Sau đó nhấn chuột vào card âm thanh được cài đặt trên PC và chọn
          <Strong>Enable</Strong>.
        </p>
        <p class="text-btn-trobel">
          B<Strong>ước 5:</Strong> Khởi động lại máy tính của bạn để kết thúc quá trình này.
        </p>
        <p><b>Khắc phục lỗi PC không nhận mic</b></p>
        <p class="text-btn-trobel"><Strong>Driver</Strong> là phần mềm quan trọng hỗ trợ người dùng sử dụng các thiết bị
          ngoại vi như loa, chuột, bàn phím,... Rất có thể driver âm thanh trên máy tính của bạn đã gặp vấn đề, cần phải
          cài đặt lại
          để có thể sử dụng.
        </p>
        <p class="text-btn-trobel">
          Nếu trong quá trình sử dụng PC nhưng bạn lại gặp tình trạng máy tính không nhận mic thì cũng đừng lo lắng và
          hãy thử một số cách khắc phục dưới đây.
        </p>
        <p class="text-btn-trobel">
          <Strong>1. Kiểm tra đầu vào của microphone trên máy tính</Strong>
        </p>
        <p class="text-btn-trobel">Để kiểm tra xem microphone của bạn có hoạt động hay không hãy thực hiện các bước sau:
        </p>
        <p class="text-btn-trobel"><Strong>Bước 1:</Strong> Nhấn tổ hợp phím <Strong>Windows + I.</Strong> </p>
        <p class="text-btn-trobel"><Strong>Bước 2:</Strong> Tìm và nhấn chọn vào mục <Strong>Sound</Strong> tại cửa sổ
          System. </p>
        <p class="text-btn-trobel"><Strong>Bước 3:</Strong> Mở cửa sổ <Strong>Microphone</Strong>. </p>
        <p class="text-btn-trobel"><Strong>Bước 4:</Strong> Click chọn vào ô Start test tại mục <Strong>Test your
            microphone.</Strong>
          <img class="camera-view-trobel" src="\images\header\image 259.png" alt="Avatar" />
        </p>
        <p class="text-btn-trobel"><Strong>Bước 5:</Strong> Quan sát chuyển động của thanh micro tại mục<Strong> Input
            volume</Strong>.</p>
        <p class="text-btn-trobel"><Strong>Lưu ý:</Strong> Nếu thanh micro chuyển động có nghĩa là laptop, máy tính của
          bạn đang hoạt
          động bình thường. Ngược lại nếu không chuyển động có thể là do gặp lỗi ở đâu đó khiến hệ thống không nhận được
          đường truyền micro.
        </p>
        <p class="text-btn-trobel">
          <Strong>2. Khởi động lại hệ thống để bật mic laptop, máy tính</Strong>
        </p>
        <p class="text-btn-trobel">Đôi khi trong quá trình sử dụng máy tính khiến hệ thống xảy ra xung đột hoặc mắc
          phải tình trạng bị giật lag. Vậy nên cách đơn giản nhất để micro có thể hoạt động lại bình thường là khởi động
          lại laptop, máy tính của bạn. </p>
      </div>
      <div class="popup-footer-trobel">
        <button class="confirm-btn-trobel" (click)="showPopupTroble = false; ">Tôi đã
          hiểu</button>
      </div>
    </div>
  </div>