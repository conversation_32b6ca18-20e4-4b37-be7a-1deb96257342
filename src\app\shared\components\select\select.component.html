<div appClickOutside (clickOutside)="isDropdown = false" class="select parent-hover"
    [class]="{'select-focus': isDropdown}" (click)="toggleDropdownGender()"
>
    <div class="flex standard-txt items-center gap-1">
        @if(list[selected].icon) {
            <img class="w-7 h-5 mr-1" [src]="list[selected].icon" alt="Migii Icon">
        }
        @if(list[selected].label) {
            <span class="font-body standard-txt font-[400]"> {{ list[selected].label }}</span>
        }
    </div>
    <app-svg-icon name="arr_down" [fill]="'--fg-secondary'" [width]="20" [height]="20"></app-svg-icon>
    @if(isDropdown){
        <div class="child select-child w-full top-[44px] right-0 z-[2]">
            @for(item of list; track item; let idx = $index){
                <div class="flex p-[6px] m-[6px] justify-between items-center rounded"
                    [class]="{'child-selected': selected === idx}" (click)="handleGenChange(idx, $event)">
                    <div class="flex items-center">
                        @if(item.icon) {
                            <img class="w-7 h-5 mr-1" [src]="item.icon" alt="Migii Icon">
                        }
                        @if(item.label) {
                            <span>{{ item.label }}</span>
                        }
                    </div>
                    @if(selected === idx){
                        <app-svg-icon name="tick_no_bg" [width]="12" [height]="9" fill="none"></app-svg-icon>
                    }
                </div>
            }
        </div>
    }
</div>