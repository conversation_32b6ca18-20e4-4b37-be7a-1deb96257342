import { Component } from '@angular/core';
import { ModalComponent } from '../../../shared/components/modal/modal.component';
import { FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { SvgIconComponent } from '../../../shared/components/svg-icon/svg-icon.component';
import { LoginSocialComponent } from '../login-social/login-social.component';
import { CommonService } from '../../../shared/services/common.service';
import { BroadcasterService } from '../../../shared/services/broadcaster.service';

@Component({
    selector: 'app-register',
    standalone: true,
    imports: [
        ModalComponent,
        ReactiveFormsModule,
        SvgIconComponent,
        LoginSocialComponent
    ],
    templateUrl: './register.component.html',
    styleUrls: [
        '../../../shared/styles/button.css',
		'../../../shared/styles/input.css',
    ]
})
export class RegisterComponent {

    loading: boolean = false;
    submitted: boolean = false;
    showPassword: boolean = false;
    showRePassword: boolean = false;
    notiErr: string = '';
    dataSignup!: FormGroup;

    constructor(
        protected readonly commonService: CommonService,
        private broadcaster: BroadcasterService
    ) {}

    ngOnInit() {
        this.dataSignup = new FormGroup({
            name: new FormControl('', [ Validators.required, Validators.minLength(2), Validators.maxLength(100) ]),
            email: new FormControl('', [Validators.required, Validators.email]),
            password: new FormControl('', [Validators.required, Validators.minLength(6) ]),
            rePassword: new FormControl('', [Validators.required, Validators.minLength(6) ]),
            agreeTerm: new FormControl('')
        });
    }

    ngAfterViewInit() {
        // this.commonService.openModal('modal-register');
    }

    openAuth() {
        this.broadcaster.broadcast('auth');
    }
}
