@if(typeShow) {
    <div class="flex items-center justify-center gap-3">
        <div class="p-2 bg-bg-primary rounded-lg flex items-center justify-center gap-2 pointer" (click)="loginWithApple()" id="apple-sign-in-btn">
            <img class="w-6 h-6" alt="icon apple" src="/images/icons/ic_apple.svg">
        </div>
        <div class="p-2 bg-bg-primary rounded-lg flex items-center justify-center gap-2 pointer" (click)="handleCredentialResponse()" id="google-sign-in-btn" #btnLoginGG>
            <img class="w-6 h-6" src="/images/icons/ic_gmail.svg" alt="icon google">
        </div>
    </div>
} @else {
    <div>
        <div class="bg-bg-primary rounded-lg h-12 flex items-center justify-center gap-2 pointer mb-3" (click)="loginWithApple()" id="apple-sign-in-btn">
            <img class="w-6 h-6" alt="icon apple" src="/images/icons/ic_apple.svg">
            <span>Login with Apple</span>
        </div>
        <div class="bg-bg-primary rounded-lg h-12 flex items-center justify-center gap-2 pointer" (click)="handleCredentialResponse()" id="google-sign-in-btn" #btnLoginGG>
            <img class="w-6 h-6" src="/images/icons/ic_gmail.svg" alt="icon google">
            <span>Login with Google</span>
        </div>
    </div>
}
@if(!loadDone) {
    <div class="loader fixed top-[calc(50%_-_25px)] left-[calc(50%_-_25px)]"></div>
}