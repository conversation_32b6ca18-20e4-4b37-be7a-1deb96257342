.popup-backdrop {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.popup-card {
  background: var(--Background-bg-white, #fff);
  border-radius: 12px;
  width: 600px;
  padding: 24px;
  position: relative;
  text-align: center;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.logo {
  width: 60px;
  height: 38px;
  object-fit: contain;
  margin-bottom: 8px;
}

.instruction {
  text-align: left;
  background: #f5f5f5;
  padding: 12px;
  border-radius: 8px;
  margin-top: 12px;
  font-size: 14px;
}

ul {
  padding-left: 20px;
  margin: 8px 0 0;
}

li {
  margin-bottom: 6px;
}


.start-btn {
  margin-top: 16px;
  background: #00b894;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 10px 18px;
  font-weight: bold;
  cursor: pointer;
  width: 100%;
}

&:hover {
  background: #019874;
}


.avatars {
  position: absolute;
  right: 12px;
  bottom: 12px;
  display: flex;
  gap: 4px;
}

.avatar {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  color: white;
  font-size: 14px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar.orange {
  background: #f39c12;
}

.avatar.blue {
  background: #34495e;
}

.modal-container3 {
  border-radius: var(--Radius-radius-16, 16px);
  max-width: 711px;
  overflow: hidden;
  padding: var(--Spacing-spacing-0, 0);
  gap: var(--Spacing-spacing-0, 0);
  background-color: var(--Background-bg-white, #fff);
}

.modal-header3 {
  min-width: var(--measurements-gap-144, 144px);
  justify-content: center;
  align-items: stretch;
  display: flex;
  width: 100%;
  flex-direction: column;
  padding: var(--Spacing-spacing-20, 20px);
  gap: var(--Spacing-spacing-16, 16px);
}

@media (max-width: 991px) {
  .modal-header3 {
    max-width: 100%;
  }
}

.logo-wrapper3 {
  align-self: center;
  display: flex;
  min-height: 63px;
  width: 100px;
  max-width: 100%;
  flex-direction: column;
  overflow: hidden;
  align-items: stretch;
  justify-content: center;
}

.logo-image3 {
  width: 100%;
  height: auto;
  object-fit: contain;
  aspect-ratio: 1.61;
}

.header-content3 {
  align-items: center;
  display: flex;
  margin-top: 16px;
  width: 100%;
  flex-direction: column;
  color: var(--Content-ct-primary, #1c2024);
  text-align: center;
  justify-content: start;
  gap: var(--Spacing-spacing-4, 4px);
  font: 500 18px/2 Inter, -apple-system, Roboto, Helvetica, sans-serif;
}

@media (max-width: 991px) {
  .header-content3 {
    max-width: 100%;
  }
}

.header-title3 {
  color: var(--Content-ct-primary, #1c2024);
  font: var(--Font-weight-Medium, 500) var(--Font-size-xl, 18px) / var(--Font-line-height-lg, 28px) var(--Font-family-Title, Inter);
}

@media (max-width: 991px) {
  .header-title3 {
    max-width: 100%;
  }
}

.modal-body3 {
  justify-content: center;
  align-items: center;
  display: flex;
  width: 100%;
  flex-direction: column;
  padding: var(--Spacing-spacing-0, 0) var(--Spacing-spacing-20, 20px);
  font: 14px/1 Inter, -apple-system, Roboto, Helvetica, sans-serif;
}

@media (max-width: 991px) {
  .modal-body3 {
    max-width: 100%;
  }
}

.body-content3 {
  align-items: stretch;
  border-radius: var(--Radius-radius-8, 8px);
  display: flex;
  max-width: 100%;
  width: 671px;
  flex-direction: column;
  justify-content: center;
  padding: var(--Spacing-spacing-12, 12px) var(--Spacing-spacing-16, 16px);
  gap: var(--Spacing-spacing-16, 16px);
  background-color: var(--Background-bg-primary, #f9f9fb);
}

.instruction-content3 {
  width: 100%;
  gap: var(--Spacing-spacing-12, 12px);
}

.instruction-title3 {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: start;
}

@media (max-width: 991px) {
  .instruction-title3 {
    max-width: 100%;
  }
}

.title-wrapper3 {
  align-self: start;
  display: flex;
  align-items: center;
  gap: 2px;
  color: var(--Content-ct-primary, #1c2024);
  font-weight: 500;
  justify-content: start;
}

.main-instruction3 {
  color: var(--Content-ct-primary, #1c2024);
  align-self: stretch;
  margin: auto 0;
  font: var(--Font-weight-Medium, 500) var(--Font-size-md, 14px) / var(--Font-size-2xl, 20px) var(--Font-family-Body, Inter);
}

.question-section3 {
  align-items: center;
  display: flex;
  margin-top: 6px;
  gap: var(--Spacing-spacing-4, 4px);
  justify-content: start;
  flex-wrap: wrap;
}

@media (max-width: 991px) {
  .question-section3 {
    max-width: 100%;
  }
}

.question-number3 {
  align-self: stretch;
  display: flex;
  align-items: center;
  gap: 2px;
  color: var(--Content-ct-info-primary, #0072d7);
  font-weight: 500;
  justify-content: start;
  margin: auto 0;
}

.question-label3 {
  color: var(--Content-ct-info-primary, #0072d7);
  align-self: stretch;
  margin: auto 0;
  font: var(--Font-weight-Medium, 500) var(--Font-size-md, 14px) / var(--Font-size-2xl, 20px) var(--Font-family-Body, Inter);
}

.question-description3 {
  align-self: stretch;
  display: flex;
  min-width: 240px;
  align-items: center;
  gap: 2px;
  color: var(--Content-ct-primary, #1c2024);
  font-weight: 400;
  justify-content: start;
  margin: auto 0;
}

@media (max-width: 991px) {
  .question-description3 {
    max-width: 100%;
  }
}

.question-text3 {
  color: var(--Content-ct-primary, #1c2024);
  align-self: stretch;
  margin: auto 0;
  font: var(--Font-weight-Regular, 400) var(--Font-size-md, 14px) / var(--Font-line-height-sm, 20px) var(--Font-family-Body, Inter);
}

@media (max-width: 991px) {
  .question-text3 {
    max-width: 100%;
  }
}

.instruction-list3 {
  margin-top: 12px;
  width: 100%;
  color: #80838d;
  font-weight: 400;
  gap: var(--Spacing-spacing-8, 8px);
}

@media (max-width: 991px) {
  .instruction-list3 {
    max-width: 100%;
  }
}

.instruction-item3 {
  display: flex;
  width: 100%;
  align-items: center;
  gap: 2px;
  line-height: 20px;
  justify-content: start;
}

.instruction-item3:not(:first-child) {
  margin-top: 8px;
}

@media (max-width: 991px) {
  .instruction-item3 {
    max-width: 100%;
  }
}

.instruction-text3 {
  align-self: stretch;
  flex: 1;
  flex-shrink: 1;
  flex-basis: 0%;
  margin: auto 0;
  font: var(--Font-weight-Medium, 500) var(--Font-size-md, 14px) / var(--Font-size-2xl, 20px) var(--Font-family-Body, Inter);
}

@media (max-width: 991px) {
  .instruction-text3 {
    max-width: 100%;
  }
}

.highlight-text3 {
  font-weight: 500;
  color: rgba(28, 32, 36, 1);
}

.modal-footer3 {
  align-items: start;
  display: flex;
  width: 100%;
  gap: var(--Spacing-spacing-12, 12px);
  color: var(--Content-ct-white, #fff);
  justify-content: start;
  padding: var(--Spacing-spacing-20, 20px);
  font: 500 14px/1 Inter, -apple-system, Roboto, Helvetica, sans-serif;
}

@media (max-width: 991px) {
  .modal-footer3 {
    max-width: 100%;
  }
}

.primary-button3 {
  justify-content: center;
  align-items: center;
  border-radius: var(--Radius-radius-8, 8px);
  display: flex;
  min-width: 240px;
  min-height: 40px;
  width: 100%;
  gap: var(--Spacing-spacing-8, 8px);
  overflow: hidden;
  flex: 1;
  flex-shrink: 1;
  flex-basis: 0%;
  padding: var(--Spacing-spacing-12, 12px) var(--Spacing-spacing-8, 8px);
  background-color: var(--Background-bg-brand-solid, #00b2a5);
  cursor: pointer;
  border: none;
}

@media (max-width: 991px) {
  .primary-button3 {
    max-width: 100%;
  }
}

.button-content3 {
  justify-content: center;
  align-items: center;
  align-self: stretch;
  display: flex;
  gap: var(--Spacing-spacing-4, 4px);
  padding: var(--Spacing-spacing-0, 0);
  margin: auto 0;
}

.button-text-wrapper3 {
  align-self: stretch;
  display: flex;
  align-items: start;
  gap: 4px;
  justify-content: start;
  margin: auto 0;
}

.main-instruction {
  display: flex;
  align-items: center;
  gap: 2px;
  color: var(--Content-ct-primary, #1c2024);
  font-weight: 500;
  justify-content: start;
}

.instruction-text {
  color: var(--Content-ct-primary, #1c2024);
  align-self: stretch;
  margin: auto 0;
  font: var(--Font-weight-Medium, 500) var(--Font-size-md, 14px) / var(--Font-size-2xl, 20px) var(--Font-family-Body, Inter);
}

.question-section {
  align-items: start;
  align-self: start;
  display: flex;
  margin-top: 4px;
  gap: var(--Spacing-spacing-4, 4px);
  justify-content: start;
}

.question-number {
  display: flex;
  align-items: center;
  gap: 2px;
  color: var(--Content-ct-info-primary, #0072d7);
  font-weight: 500;
  justify-content: start;
}

.question-label {
  color: var(--Content-ct-info-primary, #0072d7);
  align-self: stretch;
  margin: auto 0;
  font: var(--Font-weight-Medium, 500) var(--Font-size-md, 14px) / var(--Font-size-2xl, 20px) var(--Font-family-Body, Inter);
}

.question-description {
  display: flex;
  align-items: center;
  gap: 2px;
  color: var(--Content-ct-primary, #1c2024);
  font-weight: 400;
  justify-content: start;
}

.description-text {
  color: var(--Content-ct-primary, #1c2024);
  align-self: stretch;
  margin: auto 0;
  font: var(--Font-weight-Regular, 400) var(--Font-size-md, 14px) / var(--Font-line-height-sm, 20px) var(--Font-family-Body, Inter);
}

/* Instructions container */
.instructions-container {
  margin-top: 12px;
  width: 100%;
  color: var(--Content-ct-secondary, #80838d);
  font-weight: 400;
  line-height: 20px;
  gap: var(--Spacing-spacing-8, 8px);
}

.instruction-section {
  display: flex;
  margin-top: 8px;
  width: 100%;
  align-items: center;
  gap: 2px;
  justify-content: start;
}

.instruction-content {
  display: flex;
  align-items: center;
  gap: 2px;
  justify-content: start;
  width: 100%;
}

.instruction-section .instruction-text {
  color: var(--Content-ct-secondary, #80838d);
  align-self: stretch;
  flex: 1;
  flex-shrink: 1;
  flex-basis: 0%;
  margin: auto 0;
  font: var(--Font-weight-Regular, 400) var(--Font-size-md, 14px) / var(--Font-line-height-sm, 20px) var(--Font-family-Body, Inter);
}

/* Bold text styling */
.bold-text {
  font-weight: 500;
  color: rgba(28, 32, 36, 1);
}

/* List styling */
ul {
  margin: 8px 0 0 20px;
  padding: 0;
}

li {
  margin-bottom: 4px;
}

/* Responsive design */
@media (max-width: 991px) {
  .instructions-container {
    max-width: 100%;
  }

  .instruction-section {
    max-width: 100%;
  }

  .instruction-content {
    max-width: 100%;
  }

  .instruction-section .instruction-text {
    max-width: 100%;
  }
}

.text-tutorial {
  border-radius: 12px;
  color: var(--Content-ct-secondary, #80838D);
}