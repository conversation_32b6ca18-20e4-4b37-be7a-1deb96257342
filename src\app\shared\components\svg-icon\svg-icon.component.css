:host ::ng-deep .svg-container svg {
    width: 100%;
    height: 100%;
}
.box-question .active :host ::ng-deep svg {
    fill: var(--icon-blue) !important;
}
.btn-toggle .toggle-item.active :host ::ng-deep svg {
    fill: var(--icon-white) !important;
}
.menu-mb .item-menu :host ::ng-deep svg {
    fill: var(--icon-primary) !important;
}
.menu-mb .item-menu.active-menu :host ::ng-deep svg {
    fill: var(--icon-brand-primary) !important;
}
.grammar-error :host ::ng-deep svg,
.error-input :host ::ng-deep svg,
.box-question .error.active :host ::ng-deep svg {
    fill: var(--icon-error-primary) !important;
}
.grammar-correct :host ::ng-deep svg,
.box-question .correct :host ::ng-deep svg {
    fill: var(--icon-success-primary) !important;
}
.box-source .source.active :host ::ng-deep svg {
    fill: var(--icon-brand-primary) !important;
}