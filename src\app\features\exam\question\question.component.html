<div class="exam-container">
  <!-- Cột 1: <PERSON><PERSON> s<PERSON>ch câu hỏi -->
  <div *ngIf="!goHome" class="column column-left">
    <div class="section" *ngFor="let group of questionGroups; let i = index">
      <div>
        <h4>{{ group.name_label }}</h4>
        <div [ngClass]="{
            'question-boxes11': i > 0 ,
            'question-boxes21': i == 0
          }" *ngFor="let part of group.parts ; let ind = index">
          <h5 *ngIf="part.name_label">{{ part.name_label }}</h5>
          <div class="question" *ngFor="let q of part.parts; let ind = index" [ngClass]="{
            'done': q.status === 'done',
            'active': q.status === 'active'
          }">
            {{ q.question_number }}
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Cột 2: Nội dung câu hỏi + Ghi âm -->
  <div *ngIf="!goHome" class="column">
    <div class="column column-center">
      <div class="question-section">
        <p class="question-text">
          {{ questionGroups[currentGroupIndex]?.title }}
        </p>
        <ng-container *ngIf="currentGroupIndex == 0">
          <div class="content-question">
            <h2 class="vocabulary-text">{{currentQuestionContent?.content?.question }}</h2>
            <p class="vocabulary-mean">{{ currentQuestionContent?.content?.translation }}</p>
          </div>
        </ng-container>
        <ng-container *ngIf="currentGroupIndex == 1">
          <div class="content-question2">
            <p>{{ currentQuestionContent?.content?.question }}</p>
          </div>
        </ng-container>
        <ng-container *ngIf="currentGroupIndex == 2">
          <div class="content-question3">
            <div *ngIf="currentGroupIndexLevel2 == 0" class="audio-player-card">
              <div class="sound-icon-container">
                <img [src]="waitingToListen 
      ? '/images/header/CailoatotuiongOGG.svg' 
      : '/images/header/cailoatotuong.svg'" alt="Sound Icon" class="sound-icon-img">
              </div>
              <div *ngIf="!waitingToListen" class="progress-controls">
                <div class="play-pause-container">
                  <button class="play-pause-button" (click)="togglePlayPause()" [disabled]="!canPlayAudio">
                    <img
                      [src]="(isPlaying || waitingToListen) ? '/images/header/Startloabnai3.png' : '/images/header/nutbatdauphatnhac.png'"
                      alt="Play/Pause" width="20" height="20" class="pause-icon">
                  </button>
                </div>

                <div class="progress-section">
                  <div class="time-badge start-time">
                    <span class="time-text">{{ currentTime }}</span>
                  </div>
                  <div class="progress-bar-container" (click)="seekAudio($event)">
                    <div class="progress-bar">
                      <div class="progress-fill" [style.width.%]="progress"></div>
                      <div class="progress-handle" [style.left.%]="progress"></div>
                    </div>
                  </div>

                  <div class="time-badge end-time">
                    <span class="time-text">{{ totalTime }}</span>
                  </div>
                </div>
                <button class="volume-button">
                  <img src="/images/header/Volume-high.png" alt="Volume" width="20" height="20" class="volume-icon">
                </button>
              </div>
              <div *ngIf="waitingToListen" class="">
                <button class="replay-btn" (click)="startCountdown()">
                  <div class="countdown-container">
                    <svg class="progress-ring" width="32" height="32">
                      <circle class="progress-ring__circle" stroke="white" stroke-width="3" fill="transparent" r="14"
                        cx="16" cy="16" />
                    </svg>
                    <span class="countdownListen">{{ countdown }}</span>
                  </div>
                  <span class="btn-text">Nghe lại câu hỏi</span>
                  <img class="iconListen" src="/images/header/cainutbencanhnutnghelaiman3.svg" alt="Replay Icon">

                </button>
              </div>
            </div>
            <!-- <div *ngIf="currentGroupIndexLevel2 == 1" class="content-question4">
              <div class="border-image">
                <img class="image-question" [src]="currentQuestionContent?.content?.image">
              </div>
              <div class="suggest">
                <p class="suggest-title"> Gợi ý</p>
                <div class="suggest-column">
                  <ul class="suggest-list">
                    <li>1</li>
                    <li>2</li>
                    <li>3</li>
                  </ul>
                </div>
                <div class="suggest-column">
                  <ul class="suggest-list">
                    <li>5</li>
                    <li>6</li>
                    <li>7</li>
                  </ul>
                </div>
              </div>
            </div> -->
          </div>
        </ng-container>
        <ng-container *ngIf="currentGroupIndex == 3">
          <div class="content-question4">
            <p class="vocabulary-mean">{{currentQuestionContent?.content?.question }}</p>
          </div>
        </ng-container>
      </div>
    </div>
    <div class="column column-center2">
      <div class="record-section">
        <div class="record-box">
          <div class="icon-top">
            <img src="/images/header/Voice.png" alt="Voice">
            <img src="/images/header/Illust.png" alt="Illustration">
            <img src="/images/header/Voice-1.png" alt="Voice1">
          </div>
          <p class="record-text">
            {{ isRecording ? 'Đang ghi âm...' :
            canRecord ? 'Ấn "Bắt đầu ghi" để bắt đầu ghi âm câu trả lời' :
            'Đã hết lượt ghi âm cho câu hỏi này' }}
            <span *ngIf="!isRecording" class="attempt-counter">
              ({{ currentRecordingAttempts }}/{{ maxRecordingAttempts }})
            </span>
          </p>
          <button (click)="toggleRecording()" [disabled]="!canRecord && !isRecording"
            [class.disabled]="!canRecord && !isRecording" class="record-button">
            <img style="width: 100%; height: 100%;"
              [src]="isRecording ? '/images/header/micdangtraloi.png' : '/images/header/micrtalio.png'"
              [alt]="isRecording ? 'Dừng ghi' : 'Bắt đầu ghi'" class="record-icon">
          </button>
        </div>
      </div>
    </div>
  </div>
  <!-- Cột 3: Thông tin người dùng -->
  <div *ngIf="!goHome" class="column column-right">
    <div class="countdown-box">
      <p>Thời gian còn lại</p>
      <h3>{{ remainingTime }}s</h3>
    </div>

    <div class="camera-box">
      <div>
        <video class="camera-view" #videoElement autoplay muted playsinline></video>
        <!-- Các nút điều khiển -->
      </div>
      <div class="user-info">
        <p class="text-infor"><strong>Thông tin :</strong></p>
        <p><strong class="text-name">Họ và tên:</strong> {{ user.name }}</p>
        <p><strong class="text-name">Email:</strong> {{ user.email }}</p>
        <p><strong class="text-name">Ngày sinh:</strong> {{ user.birthdate }}</p>
      </div>
    </div>
    <button (click)="nextExam() " class="next-btn">Câu tiếp theo</button>
  </div>
  <div *ngIf="goHome" class="colum-done">
      <img class="img-done" src="/images/header/Logo JOPT.png" alt="">
      <div class="text-done">
      <p class="text-title-done"> <Strong>Chúc mừng bạn đã hoàn thành bài thi</Strong></p>
      <p class="text-content-done">Bạn đã hoàn thành bài thi của mình thành công! Hệ thống đã ghi nhận bài thi và sẽ gửi kết quả vào ngày <Strong>{{ today | date:'dd/MM/yyyy' }}</Strong> </p>
        <div class="buttons" >
      <button style="margin-left: 18%;" class="confirm" (click)="onGoHome()">Về Trang Chủ </button>
    </div>
     </div>
</div>


<!-- // popup -->

<div *ngIf="isConfirmEndExam == 2|| isConfirmEndExam == 1" class="overlay">
  <div class="popup">
    <!-- Icon -->
    <div class="icon" *ngIf="isConfirmEndExam == 2">ℹ️</div>
    <div class="icon warning" *ngIf="isConfirmEndExam == 1">❗</div>

    <!-- Tiêu đề -->
    <h3 class="title" *ngIf="isConfirmEndExam == 2">Xác nhận nộp bài sớm</h3>
    <h3 class="title" *ngIf="isConfirmEndExam == 1">Đã hết thời gian làm bài</h3>

    <!-- Mô tả -->
    <p class="description" *ngIf="isConfirmEndExam == 2">
      Bạn đang lựa chọn nộp bài thi trước thời gian quy định. Sau khi nộp, bạn sẽ không thể quay lại chỉnh sửa bài làm.
    </p>
    <p class="description" *ngIf="isConfirmEndExam == 1">
      Hệ thống đã ghi nhận kết thúc thời gian làm bài. Thí sinh vui lòng dừng mọi thao tác và tiến hành nộp bài ngay lập tức.
    </p>

    <!-- Nút -->
    <div class="buttons" *ngIf="isConfirmEndExam == 2">
      <button class="cancel" (click)="onCancel()">Kiểm tra lại</button>
      <button class="confirm" (click)="goHome = true ; isConfirmEndExam = 0">Xác nhận nộp bài</button>
    </div>

    <div class="buttons" *ngIf="isConfirmEndExam == 1">
      <button class="submit" (click)="goHome = true; isConfirmEndExam = 0">Nộp bài</button>
    </div>

  </div>
</div> 

