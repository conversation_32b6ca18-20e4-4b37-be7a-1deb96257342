<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1210_5031)">
<g filter="url(#filter0_di_1210_5031)">
<path d="M1.19995 11.9992C1.19995 6.03454 6.03528 1.19922 12 1.19922C17.9646 1.19922 22.8 6.03454 22.8 11.9992C22.8 17.9639 17.9646 22.7992 12 22.7992C6.03528 22.7992 1.19995 17.9639 1.19995 11.9992Z" fill="url(#paint0_linear_1210_5031)"/>
<path d="M1.19995 11.9992C1.19995 6.03454 6.03528 1.19922 12 1.19922C17.9646 1.19922 22.8 6.03454 22.8 11.9992C22.8 17.9639 17.9646 22.7992 12 22.7992C6.03528 22.7992 1.19995 17.9639 1.19995 11.9992Z" fill="url(#paint1_linear_1210_5031)" fill-opacity="0.16"/>
</g>
<g filter="url(#filter1_di_1210_5031)">
<path d="M7.19995 11.9992C7.19995 9.34825 9.34898 7.19922 12 7.19922C14.6509 7.19922 16.8 9.34825 16.8 11.9992C16.8 14.6502 14.6509 16.7992 12 16.7992C9.34898 16.7992 7.19995 14.6502 7.19995 11.9992Z" fill="white"/>
</g>
</g>
<defs>
<filter id="filter0_di_1210_5031" x="0.199951" y="0.199219" width="23.6001" height="23.5996" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="1" operator="dilate" in="SourceAlpha" result="effect1_dropShadow_1210_5031"/>
<feOffset/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.760784 0 0 0 0 0.960784 0 0 0 0 0.933333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1210_5031"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1210_5031" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1.05"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.3 0"/>
<feBlend mode="hard-light" in2="shape" result="effect2_innerShadow_1210_5031"/>
</filter>
<filter id="filter1_di_1210_5031" x="3.19995" y="4.19922" width="17.6001" height="18.5996" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.151067 0 0 0 0 0.140034 0 0 0 0.13 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1210_5031"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1210_5031" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-3"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.854902 0 0 0 0 0.980392 0 0 0 0 0.960784 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_1210_5031"/>
</filter>
<linearGradient id="paint0_linear_1210_5031" x1="12" y1="1.19922" x2="12" y2="22.7992" gradientUnits="userSpaceOnUse">
<stop stop-color="#21C99D"/>
<stop offset="0.5" stop-color="#11BD9E"/>
<stop offset="1" stop-color="#00B2A5"/>
</linearGradient>
<linearGradient id="paint1_linear_1210_5031" x1="12" y1="1.19922" x2="12" y2="22.7992" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<clipPath id="clip0_1210_5031">
<rect width="24" height="24" fill="white"/>
</clipPath>
</defs>
</svg>
