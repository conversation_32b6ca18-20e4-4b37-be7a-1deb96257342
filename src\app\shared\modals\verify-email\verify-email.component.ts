import { Component, ElementRef, QueryList, ViewChildren } from '@angular/core';
import { ModalComponent } from '../../components/modal/modal.component';
import { FormsModule } from '@angular/forms';
import { CommonService } from '../../services/common.service';
import { UserService } from '../../services/user.service';

@Component({
    selector: 'app-verify-email',
    standalone: true,
    imports: [ModalComponent, FormsModule],
    templateUrl: './verify-email.component.html',
    styleUrls: [
        '../../styles/input.css',
        './verify-email.component.css'
    ]
})
export class VerifyEmailComponent {
    email: string = '<EMAIL>';
    loading: boolean = false;
    isErr: boolean = false;
    isSuccess: boolean = false;

    constructor(
        private commonService: CommonService,
        private userService: UserService
    ) { }

    ngAfterViewInit() {
        this.commonService.openModal('modal-verify-email')
    }

    otp: string[] = ['', '', '', '', '', ''];

    @ViewChildren('otpInput', { read: ElementRef })
    inputs!: QueryList<ElementRef>;

    onInput(event: any, index: number) {
        const input = event.target;
        const value = input.value;

        if (value) {
            this.otp[index] = value;
            if (index < this.otp.length - 1) {
                const nextInput = this.inputs.get(index + 1);
                nextInput?.nativeElement.focus();
            }
        }
    }

    onKeyDown(event: KeyboardEvent, index: number) {
        if (event.key === 'Backspace' && !this.otp[index] && index > 0) {
            const prevInput = this.inputs.get(index - 1);
            prevInput?.nativeElement.focus();
            this.isErr = false;
        }
    }

    submit() {
        if(this.isSuccess) {
            this.commonService.closeModal('modal-verify-email');
        }
        if(this.loading || this.isSuccess) return;
        this.loading = true;
        this.userService.verifyTokenEmail(this.otp.join('')).subscribe({
            next: res => {
                this.isSuccess = true;
                this.loading = false;
            },
            error: err => {
                let notiErr = $localize `:@@txt_email_verify_fail:Incorrect verification code`;
                if(err.status === 403) {
                    notiErr = $localize `:@@txt_email_verify_expired:The verification code has expired`;
                }
                this.isErr = true;
                this.loading = false;
                this.commonService.showNotify(notiErr, 'error');
            }
        })
    }
}
