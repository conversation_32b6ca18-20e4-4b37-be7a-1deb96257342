<div class="mb-3 flex items-center justify-between ">
    <span class="text-sm text-ct-secondary" i18n="@@txt_order_value">G<PERSON><PERSON> trị đơn hàng</span>
    <span class="font-semibold">
        {{ packDetail.prices[lang].price | roundPrice:packDetail.prices[lang].currency }} {{ packDetail.prices[lang].symbol }}
    </span>
</div>
@if(packDetail.sale) {
    <div class="mb-3 flex items-center justify-between">
        <div class="flex items-center">
            <span class="text-sm text-ct-secondary" i18n="@@txt_discount">Giảm giá</span>
            <div class="text-xs leading-5 border text-ct-warning-primary bg-[var(--color-orange-2)] px-2 py-0 rounded-[99px] border-solid border-bd-warning ml-1">
                -{{ packDetail.sale.sale_percent }}%
            </div>
        </div>
        <span class="font-semibold">- {{ (packDetail.prices[lang].price * packDetail.sale.sale_percent / 100 ) | roundPrice:lang }} {{ packDetail.prices[lang].symbol }}</span>
    </div>
}
@if(lang !== 'en') {
    <div class="mb-3 flex items-center justify-between item-discount">
        <div class="flex items-center">
            <span class="text-sm text-ct-secondary one-line" i18n-title="@@txt_discount_bank" title="Giảm giá thêm khi chuyển khoản" i18n="@@txt_discount_bank">
                Giảm giá thêm khi chuyển khoản
            </span>
        </div>
        <span class="font-semibold">
            - {{ (packDetail.sale ? packDetail.sale.prices[lang] : packDetail.prices[lang].price) * 0.05 | roundPrice:packDetail.prices[lang].currency }} {{ packDetail.prices[lang].symbol }}
        </span>
    </div>
}

<!-- @if(packDetail.sale && (packDetail.prices[lang].price - packDetail.sale.prices[lang])) {
    <div class="mb-3 flex items-center justify-between ">
        <span class="text-sm text-ct-secondary" i18n="@@txt_event_voucher">Event voucher</span>
        <span class="font-semibold flex items-center">
            <span class="ml-1">
                -{{ packDetail.prices[lang].price - packDetail.sale.prices[lang] | roundPrice:packDetail.prices[lang].currency }} {{ packDetail.prices[lang].symbol }}
            </span>
        </span>
    </div>
} -->
<hr class="line my-3 text-sm text-ct-secondary" />
<div class="mb-3 flex items-center justify-between ">
    <span class="text-sm text-ct-secondary" i18n="@@txt_payment_total">Tổng thanh toán</span>
    <span class="font-semibold">{{ total }} {{ packDetail.prices[lang].symbol }}</span>
</div>

@if((lang !== 'en' ? ( packDetail.prices[lang].price - (packDetail.sale ? packDetail.sale.prices[lang] : packDetail.prices[lang].price) * 0.95 ) : 0) > 0) {
    <div class="box-saved">
        <span i18n="@@txt_yay_saved">Tuyệt vời! Bạn đã tiết kiệm </span>
        <span class="font-semibold">
            {{ ((lang !== 'en' ? ( packDetail.prices[lang].price - (packDetail.sale ? packDetail.sale.prices[lang] : packDetail.prices[lang].price) * 0.95 ) : 0)) | roundPrice:packDetail.prices[lang].currency }} {{ packDetail.prices[lang].symbol }}
        </span>
    </div>
}