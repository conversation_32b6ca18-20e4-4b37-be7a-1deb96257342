.header {
    display: flex;
    position: fixed;
    top: 0;
    left: 0;
    gap: 2.5rem;
    align-items: center;
    padding: 0.75rem 2.5rem;
    width: 100%;
    height: 4rem;
    background-color: white;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    z-index: 100;
}

.left-content {
    display: flex;
    gap: 1.25rem;
    align-items: center;
    height: 2.5rem;
    flex: 1 0 0;
}

.logo-container {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    justify-content: center;
    align-items: center;
    width: 4rem;
}

.logo-image {
    width: 100%;
    height: 2.5rem;
    aspect-ratio: 8/5;
}

.exam-name {
    display: flex;
    flex-shrink: 0;
    gap: 0.625rem;
    justify-content: center;
    align-items: center;
    width: 600px;
}

.exam-title {
    flex: 1 0 0;
    color: var(--Foreground-fg-primary, #1C2024);
    text-align: center;
    /* Standard text/Title/20/Medium */
    font-family: var(--Font-family-Title, Inter);
    font-size: var(--Font-size-2xl, 20px);
    font-style: normal;
    font-weight: var(--Font-weight-Medium, 500);
    line-height: var(--Font-line-height-lg, 28px);
    /* 140% */
}

.header-icons {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    align-items: center;
    flex: 1 0 0;
}

.icon-wrapper {
    display: flex;
    width: 1.5rem;
    height: 1.5rem;
    padding: 0.15625rem 0.125rem;
    justify-content: center;
    align-items: center;
}

/* 2 nut 
 */

.popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.popup-content {
    background: white;
    border-radius: 16px;
    width: 400px;
    max-width: 90vw;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    overflow: hidden;
}

.popup-header {
    position: relative;
    padding: 20px 20px 10px;
    text-align: center;
}

.close-btn {
    position: absolute;
    top: 15px;
    right: 15px;
    background: none;
    border: none;
    font-size: 24px;
    color: #666;
    cursor: pointer;
}

.logo {
    display: flex;
    justify-content: center;
    align-items: center;
}

.logo-img {
    width: 60px;
    height: 38px;
    object-fit: contain;
}

.popup-body {
    padding: 0 20px 20px;
}

.title {
    font-size: 18px;
    font-weight: 600;
    margin: 15px 0 8px;
    text-align: center;
    color: #333;
}

.description {
    font-size: 14px;
    color: #666;
    text-align: center;
    margin-bottom: 20px;
}

.error-options {
    margin-bottom: 20px;
}

.error-option {
    display: block;
    padding: 12px 0;
    cursor: pointer;
    display: flex;
    align-items: center;
}

.error-option:last-child {
    border-bottom: none;
}

.error-option input[type="radio"] {
    margin-right: 12px;
    accent-color: #00BFA5;
}

.option-text {
    font-size: 14px;
    color: #333;
}

.popup-actions {
    display: flex;
    gap: 12px;
}

.btn-cancel {
    display: flex;
    height: 40px;
    padding: 12px 8px;
    justify-content: center;
    align-items: center;
    gap: 8px;
    flex: 1 0 0;
    border-radius: 8px;
    border: 1px solid #ddd;
    background: white;
    color: #666;
    font-size: 14px;
    cursor: pointer;
}

.btn-submit {
    display: flex;
    height: 40px;
    padding: 12px 8px;
    justify-content: center;
    align-items: center;
    gap: 8px;
    flex: 1 0 0;
    border-radius: 8px;
    border: none;
    background: #00B2A5;
    color: white;
    font-size: 14px;
    cursor: pointer;
}

.btn-submit:disabled {
    background: #ccc;
    cursor: not-allowed;
}

/* nút setting
 */
.setting-toggle {
    position: absolute;
    top: 16px;
    right: 16px;
    cursor: pointer;
    z-index: 10;
}

.setting-popup {
    position: absolute;
    top: 56px;
    right: 16px;
    width: 280px;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    padding: 0;
    z-index: 100;
    overflow: hidden;
}

.setting-header {
    padding: 16px 20px;
    border-bottom: 1px solid #f3f4f6;
    background: #f9fafb;
}

.setting-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #374151;
}

.setting-item {
    padding: 16px 20px;
    border-bottom: 1px solid #f3f4f6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.setting-item:last-child {
    border-bottom: none;
}

.setting-label {
    flex: 1;
}

.setting-title {
    font-size: 14px;
    color: #374151;
    font-weight: 500;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #d1d5db;
    transition: .4s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked+.slider {
    background-color: #00B2A5;
}

input:checked+.slider:before {
    transform: translateX(20px);
}

.font-options {
    display: flex;
    gap: 8px;
}

.font-size-btn {
    width: 32px;
    height: 32px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    background: white;
    color: #6b7280;
    font-size: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
}

.font-size-btn:hover {
    border-color: #00B2A5;
}

.font-size-btn.active {
    background: #00B2A5;
    color: white;
    border-color: #00B2A5;
}

.login-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.login-modal {
  display: flex;
  width: 450px;
  padding: 20px;
  flex-direction: column;
  align-items: center;
  gap: 40px;
  border-radius: 16px;
  position: relative;
  font-family: "Inter", sans-serif;
  background-color: #fff;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

@media (max-width: 991px) {
  .login-modal {
    width: 100%;
    max-width: 450px;
    padding: 20px;
    margin: 20px;
  }
}

@media (max-width: 640px) {
  .login-modal {
    width: 100%;
    max-width: 400px;
    padding: 16px;
    gap: 32px;
    margin: 16px;
  }
}

.login-close-button {
  display: flex;
  padding: 5px;
  justify-content: center;
  align-items: center;
  position: absolute;
  right: 20px;
  top: 20px;
  border-radius: 8px;
  cursor: pointer;
  background-color: rgba(10, 15, 41, 0.04);
  border: none;
  transition: background-color 0.2s ease;
}

.login-close-button:hover {
  background-color: rgba(10, 15, 41, 0.08);
}

.login-close-button:focus {
  outline: 2px solid #00b2a5;
  outline-offset: 2px;
}

@media (max-width: 640px) {
  .login-close-button {
    right: 16px;
    top: 16px;
  }
}

.login-close-icon {
  width: 14px;
  height: 14px;
}

.login-modal-header {
  display: flex;
  width: 410px;
  flex-direction: column;
  align-items: flex-start;
  gap: 12px;
  position: relative;
}

@media (max-width: 991px) {
  .login-modal-header {
    width: 100%;
  }
}

@media (max-width: 640px) {
  .login-modal-header {
    width: 100%;
  }
}

.login-logo {
  width: 80px;
  height: 50px;
  aspect-ratio: 8/5;
  position: relative;
}

.login-header-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
  align-self: stretch;
  position: relative;
}

.login-title-container {
  display: flex;
  align-items: center;
  gap: 2px;
  position: relative;
}

.login-modal-title {
  color: #1c2024;
  position: relative;
  font: 600 18px/28px "Inter", sans-serif;
  margin: 0;
}

@media (max-width: 640px) {
  .login-modal-title {
    font-size: 16px;
    line-height: 24px;
  }
}

.login-description-container {
  display: flex;
  align-items: center;
  gap: 2px;
  align-self: stretch;
  position: relative;
}

.login-modal-description {
  flex: 1 0 0;
  color: #80838d;
  position: relative;
  font: 400 14px/20px "Inter", sans-serif;
  margin: 0;
}

@media (max-width: 640px) {
  .login-modal-description {
    font-size: 13px;
    line-height: 18px;
  }
}

.login-form {
  display: flex;
  width: 410px;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  position: relative;
}

@media (max-width: 991px) {
  .login-form {
    width: 100%;
  }
}

@media (max-width: 640px) {
  .login-form {
    width: 100%;
  }
}

.login-input-field-container {
  display: flex;
  padding: 0;
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
  width: 100%;
  position: relative;
}

.login-label-container {
  display: flex;
  padding: 0;
  align-items: center;
  width: 100%;
  border-radius: 5px;
  position: relative;
}

.login-field-label {
  display: flex;
  padding: 2px 0;
  align-items: flex-start;
  gap: 4px;
  flex: 1 0 0;
  position: relative;
  color: #1c2024;
  font: 400 14px/20px "Inter", sans-serif;
}

.login-required-asterisk {
  color: #d5251e;
  font: 400 14px/20px "Inter", sans-serif;
}

.login-input-wrapper {
  display: flex;
  height: 40px;
  padding: 12px;
  align-items: center;
  gap: 8px;
  width: 100%;
  border-radius: 8px;
  border: 1px solid #f0f0f3;
  position: relative;
  background-color: #fff;
  box-sizing: border-box;
}

.login-input-content {
  display: flex;
  height: 20px;
  padding: 0;
  align-items: center;
  gap: 4px;
  flex: 1 0 0;
  position: relative;
}

.login-input-icon {
  display: flex;
  width: 16px;
  height: 16px;
  justify-content: center;
  align-items: center;
  position: relative;
}

.login-typing-area {
  display: flex;
  align-items: center;
  flex: 1 0 0;
  position: relative;
}

.login-input-field {
  width: 100%;
  border: none;
  outline: none;
  background: transparent;
  color: #1c2024;
  font: 400 14px/20px "Inter", sans-serif;
}

.login-input-field::placeholder {
  color: #b9bbc6;
}

.login-password-toggle {
  display: flex;
  align-items: center;
  gap: 10px;
  position: relative;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
}

.login-password-toggle:focus {
  outline: 2px solid #00b2a5;
  outline-offset: 2px;
  border-radius: 4px;
}

.login-eye-icon {
  display: flex;
  width: 16px;
  height: 16px;
  justify-content: center;
  align-items: center;
  position: relative;
}

.login-button {
  display: flex;
  padding: 12px 8px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  align-self: stretch;
  border-radius: 8px;
  position: relative;
  cursor: pointer;
  background-color: #00b2a5;
  border: none;
  transition: background-color 0.2s ease;
}

.login-button:hover {
  background-color: #009a8e;
}

.login-button:focus {
  outline: 2px solid #00b2a5;
  outline-offset: 2px;
}

.login-button:active {
  background-color: #008a7e;
}

.login-button-content {
  display: flex;
  padding: 0;
  justify-content: center;
  align-items: center;
  gap: 4px;
  position: relative;
}

.login-button-text-container {
  display: flex;
  align-items: baseline;
  gap: 4px;
  position: relative;
}

.login-button-text {
  color: #fff;
  position: relative;
  font: 500 16px/24px "Inter", sans-serif;
}
