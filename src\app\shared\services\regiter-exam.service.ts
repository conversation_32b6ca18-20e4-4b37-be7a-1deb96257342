import { Injectable } from '@angular/core';
import * as CONFIG from '../../common/constants/config';
import { HttpClient } from '@angular/common/http';
import { CommonService } from './common.service';
import { InforRegisterExam, RoundExam } from '../../common/interfaces/regiter-exam';
import { ResMessage } from '../../common/interfaces/common';

@Injectable({
    providedIn: 'root'
})
export class RegiterExamService {

    constructor(
        private http: HttpClient,
        private commonService: CommonService
    ) { }

    uploadCccd(blob: Blob, type: 'front' | 'back') {
        const url = CONFIG.BASE_API_URL + 'jopt/user/upload-cccd';
        const formData = new FormData();
        const file = new File([blob], 'cropped.png', { type: 'image/png' });
        formData.append('file', file);
        formData.append('type', type);
        return this.http.post(url, formData);
    }

    changeUserInfor(data: InforRegisterExam) {
        const url = CONFIG.BASE_API_URL + 'jopt/user/info';
        return this.http.put(url, data, CONFIG.HTTP_OPTION);
    }

    getSchedule() {
        const url = CONFIG.BASE_API_URL + 'jopt/exam/round';
        return this.http.get<ResMessage<RoundExam[]>>(url);
    }
}
