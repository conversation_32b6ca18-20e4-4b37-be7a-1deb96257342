<app-modal id="modal-verify-email">
    <div class="relative mx-auto w-[484px] max-w-[90%] rounded-2xl bg-bg-white p-5">
        <img class="mx-auto w-[100px] h-[62.5px]" src="/images/jopt/jopt.svg" alt="Migii JOPT">
        @if(isSuccess) {
            <div class="text-lg font-medium text-center mb-1" i18n="@@txt_email_verify_success">Email verification successful</div>
        } @else {
            <div class="text-lg font-medium text-center mb-1" i18n="@@txt_email_verify">Email Verification</div>
            <div class="text-sm text-ct-secondary mb-5" i18n="@@txt_email_verify_des">
                A verification code has been sent to your email <span class="text-ct-link-primary">{{ email }}</span>. Enter the 6-digit verification code to continue
            </div>
            <div class="flex items-center justify-center gap-3">
                @for (item of otp; track $index) {
                    <input type="text"
                        class="otp-input input w-11 h-11 text-center"
                        [class.input-err]="isErr"
                        maxlength="1"
                        [(ngModel)]="otp[$index]"
                        (input)="onInput($event, $index)"
                        (keydown)="onKeyDown($event, $index)"
                        #otpInput
                    />
                }
            </div>
            <div class="text-sm mt-4 pb-5">
                <span class="text-ct-secondary">Didn't receive the code?</span>&nbsp;
                <span class="font-medium">Gửi lại sau 30s</span>
            </div>
        }
        <div (click)="submit()" class="text-center text-sm text-ct-white pointer bg-bg-brand-solid rounded-lg py-2.5 mt-5">Next</div>
        @if(loading) {
            <div class="loader absolute top-[calc(50%_-_25px)] left-[calc(50%_-_25px)]"></div>
        }
    </div>
</app-modal>
