import { ObjectKey } from "../interfaces/common";


export interface ResCountryCode {
    countryCode: string;
}
export interface CreditCard {
    id: string;
    selectStatus: boolean;
    scriptsLoaded: boolean;
} 

export interface Scripts {
    name: string;
    src: string;
    DOMElement: string;
}

export interface SalePackage<T> {
    name: string,
    productId: string,
    id: string,
    prices: ObjectKey<Price>,
    discountPercent: number,
    sale: T;
}

export interface Price {
    price: number,
    currency: string,
    symbol: string
}

export interface SaleItem {
    prices: ObjectKey<number>,
    sale_percent: number,
    productId: string
}

interface SaleInf {
    premium: string,
    percent: string
}
interface Ads {
    ad_id: number,
    ad_group_id: number,
    country: string,
    language: string,
    daily: number,
    sale_android: SaleInf[],
    sale_web: SaleInf[],
    end_android: number,
    start_android: number,
    timeServer: number
}
export interface InforSale {
    status: number,
    Ads: Ads
}

export interface InforSalePackage {
    end: number,
    start: number,
    timeServer: number,
    packages: SalePackage<SaleItem | undefined>[]
}

export interface QRCodePay {
    transaction_code: string,
    qrcode_url: string
}

export interface InforPay {
    user_id: string,
    product_id: string,
    price: number,
    currency: string,
    transaction_channel: 'WEB'
}

export interface UserUpgrade {
    name: string,
    avatar: string
}
export interface ListUserUpgrade {
    total: number,
    info: UserUpgrade[]
}
export interface ResListUserUpgrade {
    message: string,
    data: ListUserUpgrade,
}

export const LIST_CREDIT: CreditCard[] = [
    {
        id: 'paypal',
        selectStatus: false,
        scriptsLoaded:  false,
    }
];

export interface InforPayACB {
    product_id: string, //migii_jlpt_6months_50
    price: number,
    affiliate: {
        affiliate_code: any,
        affiliate_package_key: any,
        affiliate_discount: any
    }
}

export interface InforPayExamJoptACB {
    product_id: "mytest_jopt",
    price: 1970000,
    attachment: {
        slot_id: string
    }
}

export const ORIGINAL_PACKAGES: ObjectKey<SalePackage<ObjectKey<SaleItem>>> = {
    pre12months: {
        name: $localize `:@@txt_1_year:1 year`,
        productId: 'mytest_jlpt_pre_1year_sale50',
        id: 'pre12months',
        prices: {
            vi: {
                price: 839000,
                currency: 'VND',
                symbol: '₫'
            },
            en: {
                price: 39.99,
                currency: 'USD',
                symbol: '$'
            },
            ru: {
                price: 3499,
                currency: 'RUB',
                symbol: '₽'
            },
            ko: {
                price: 46000,
                currency: 'KRW',
                symbol: '₩'
            },
            zh: {
                price: 298,
                currency: 'CNY',
                symbol: '¥'
            },
            id: {
                price: 309000,
                currency: 'IDR',
                symbol: 'Rp'
            },
            'zh-Hant': {
                price: 1070,
                currency: 'TWD',
                symbol: 'NT$'
            },
            ja: {
                price: 5700,
                currency: 'JPY',
                symbol: '¥'
            }
        },
        discountPercent: 50,
        sale: {
            "70" : {
                prices: {
                    vi: 739000,
                    en: 34.99,
                    ru: 3090,
                    ko: 40000,
                    zh: 228,
                    id: 269000,
                    'zh-Hant': 900,
                    ja: 5200
                },
                sale_percent: 70,
                productId: 'mytest_jlpt_pre_1year_sale70'
            },
            "90" : {
                prices: {
                    vi: 649000,
                    en: 29.99,
                    ru: 2690,
                    ko: 34000,
                    zh: 198,
                    id: 229000,
                    'zh-Hant': 780,
                    ja: 4700
                },
                sale_percent: 90,
                productId: 'mytest_jlpt_pre_1year_sale90'
            }
        }
    },
    preforevermonths: {
        name: $localize `:@@txt_lifetime:Lifetime`,
        productId: 'mytest_jlpt_pre_lifetime_sale50',
        id: 'preforevermonths',
        prices: {
            vi: {
                price: 2499000,
                currency: 'VND',
                symbol: '₫'
            },
            en: {
                price: 119.99,
                currency: 'USD',
                symbol: '$'
            },
            ru: {
                price: 10790,
                currency: 'RUB',
                symbol: '₽'
            },
            ko: {
                price: 138000,
                currency: 'KRW',
                symbol: '₩'
            },
            zh: {
                price: 798,
                currency: 'CNY',
                symbol: '¥'
            },
            id: {
                price: 929000,
                currency: 'IDR',
                symbol: 'Rp'
            },
            'zh-Hant': {
                price: 3200,
                currency: 'TWD',
                symbol: 'NT$'
            },
            ja: {
                price: 17000,
                currency: 'JPY',
                symbol: '¥'
            }
        },
        discountPercent: 50,
        sale: {
            "70" : {
                prices: {
                    vi: 2249000,
                    en: 99.99,
                    ru: 8899,
                    ko: 120000,
                    zh: 698,
                    id: 809000,
                    'zh-Hant': 2700,
                    ja: 15400
                },
                sale_percent: 70,
                productId: 'mytest_jlpt_pre_lifetime_sale70'
            },
            "90" : {
                prices: {
                    vi: 1949000,
                    en: 89.99,
                    ru: 8299,
                    ko: 102000,
                    zh: 598,
                    id: 629000,
                    'zh-Hant': 2350,
                    ja: 14000
                },
                sale_percent: 90,
                productId: 'mytest_jlpt_pre_lifetime_sale90'
            }
        }
    },
    pre3months: {
        name: $localize `:@@txt_3_months:3 months`,
        productId: 'mytest_jlpt_pre_3months_sale10',
        id: 'pre3months',
        prices: {
            vi: {
                price: 479000,
                currency: 'VND',
                symbol: '₫'
            },
            en: {
                price: 25.99,
                currency: 'USD',
                symbol: '$'
            },
            ru: {
                price: 2299,
                currency: 'USD',
                symbol: '$'
            },
            ko: {
                price: 29000,
                currency: 'KRW',
                symbol: '₩'
            },
            zh: {
                price: 198,
                currency: 'CNY',
                symbol: '¥'
            },
            id: {
                price: 199000,
                currency: 'IDR',
                symbol: 'Rp'
            },
            'zh-Hant': {
                price: 690,
                currency: 'TWD',
                symbol: 'NT$'
            },
            ja: {
                price: 3700,
                currency: 'JPY',
                symbol: '¥'
            }
        },
        discountPercent: 10,
        sale: {}
    }
}
