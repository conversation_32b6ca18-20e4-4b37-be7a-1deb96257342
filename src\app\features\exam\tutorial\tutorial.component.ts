import { Component, Input, input } from '@angular/core';
import { EventEmitter, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
@Component({
  selector: 'app-tutorial',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './tutorial.component.html',
  styleUrl: './tutorial.component.css'
})
export class TutorialComponent {
  @Output() dataSentTutorial = new EventEmitter<any>();
  @Input() exam: number = 1;
  @Input() partExam: number = 1; // 1: DoExam, 2: Question, 3: Result
  data: any = {
    exam: this.exam,
    timeToReadSingleWord: 10,
    timeToReadParagraph: 180,
    timeToListenSingleWord: 60,
    timeToAnser: 60,
    timeToRecordAnswer: 60
  };
  nextExam() {
    this.dataSentTutorial.emit(this.data);
  }
}
