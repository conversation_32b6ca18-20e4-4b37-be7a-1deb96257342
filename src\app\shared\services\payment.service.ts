import { Injectable } from '@angular/core';
import { CommonService } from '../../shared/services/common.service';
import * as CONFIG from '../../common/constants/config';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, map, Observable, of, tap } from 'rxjs';
import { environment } from '../../../environments/environment';
import { ResMessage } from '../../common/interfaces/common';
import { CreditCard, InforPayACB, InforPayExamJoptACB, InforSale, InforSalePackage, LIST_CREDIT, ORIGINAL_PACKAGES, QRCodePay, SaleItem, SalePackage, Scripts } from '../../common/interfaces/payment';

declare var document: any;

export const ScriptStore: Scripts[] = environment.SCRIPT_STORE;

@Injectable({
    providedIn: 'root'
})
export class PaymentService {

    public onLoadObject: any;
    private scripts: any = {};
    paymentMethod: CreditCard[] = LIST_CREDIT;
    saleInformation: InforSalePackage | undefined;

    packDetail: SalePackage<SaleItem | undefined> | undefined;
    langPriceNew: string = 'en';
    transaction_code: string = '';
    payMethod: 'bank' | 'paypal' = 'bank';
    statusUgrade: boolean = false;
    roudExamId: string = '';
    slotExamId: string = '';

    loadPayLibrary = new BehaviorSubject<string>('');
    
    // salePackage: SalePackage<number>[] = [];

    constructor(
        private commonService: CommonService,
        private http: HttpClient
    ) {
        ScriptStore.forEach((script: any) => {
            this.scripts[script.name] = {
                loaded: false,
                src: script.src,
                DOMElement: script.DOMElement
            };
        });
    }

    load(...scripts: string[]) {
        const promises: any[] = [];
        scripts.forEach((script) => promises.push(this.loadScript(script)));
        return Promise.all(promises);
    }
      
    loadScript(name: string) {
        return new Promise((resolve, reject) => {

            if (!this.scripts[name].loaded && this.commonService.getEnvironment() === 'client') {
                //load script
        
                let script = document.createElement('script');
                script.type = 'text/javascript';
                script.src = this.scripts[name].src;

                if (script.readyState) {  //IE
        
                    script.onreadystatechange = () => {
        
                        if (script.readyState === "loaded" || script.readyState === "complete") {
                            script.onreadystatechange = null;
                            this.scripts[name].loaded = true;
                            resolve({script: name, loaded: true, status: 'Loaded'});
                        }
                    };
        
                } else {  //Others
        
                    script.onload = () => {
                        this.scripts[name].loaded = true;
                        resolve({script: name, loaded: true, status: 'Loaded'});
                    };
                }
        
                script.onerror = (error: any) => resolve({script: name, loaded: false, status: 'Loaded'});
                document.getElementsByTagName(this.scripts[name].DOMElement)[0].appendChild(script);
        
            } else {
                resolve({ script: name, loaded: true, status: 'Already Loaded' });
            }
        });
    }

    initpaymentMethod() {
        this.paymentMethod.forEach((item, key) => {
            this.loadPaymentMethodScript(item.id, key);
        });
    }

    loadPaymentMethodScript(methodKey: string, key: number) {

        // You can load multiple scripts by just providing the key as argument into load method of the service
        this.load(methodKey).then(data => {
            this.paymentMethod[key].scriptsLoaded = true;
            this.loadPayLibrary.next(methodKey);
        })
        .catch(
            error => {
                console.log(error)
            }
        );
    }

    getInforPayJopt(data: InforPayExamJoptACB) {
        const url = CONFIG.BASE_API_URL + 'jopt/purchase/virtual-bill';
        return this.http.post<ResMessage<QRCodePay>>(url, data, CONFIG.HTTP_OPTION);
    }

    getLangPrice() {
        return this.commonService.getCountryCode().pipe(
            map(countryCode => {
                switch (countryCode) {
                    case 'vn':
                        return 'vi';
                    case 'ru':
                        return 'ru';
                    case 'cn':
                        return 'zh';
                    case 'tw':
                        return 'zh-Hant';
                    case 'id':
                        return 'id';
                    case 'ko':
                        return 'en';
                    case 'jp':
                        return 'ja';
                    default:
                        return 'en';
                }
            })
        )
    }

    downloadImage(imageUrl: string, fileName: string) {
        this.http.get(imageUrl, { responseType: 'blob' }).subscribe({
            next: blob => {
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = fileName;
                a.click();
                window.URL.revokeObjectURL(url);
            },
            error: error => {
                this.commonService.showNotify($localize `:@@txt_had_err: Something error!`, 'error');
            }
        });
    }
}
