@keyframes show_slide {
    0%{
        transform: translateX(100%);
    }
    40%{
        transform: translateX(-10%);
    }
    80%{
        transform: translateX(0%);
    }
    100%{
        transform: translateX(-10px);
    }
}
@keyframes hide_slide {
    0%{
        transform: translateX(-10px);
    }
    40%{
        transform: translateX(0%);
    }
    80%{
        transform: translateX(-10%);
    }
    100%{
        transform: translateX(100%);
    }
}
.alert {
    position: fixed;
    width: fit-content;
    right: 20px;
    bottom: 20px;
    border-radius: 12px;
    overflow: hidden;
    opacity: 0;
    pointer-events: none;
    z-index: 999;
    padding: 8px;
}
.alert.show {
    opacity: 1;
    pointer-events: auto;
    animation: show_slide 0.5s ease forwards;
}
.alert.hide {
    animation: hide_slide 0.5s ease forwards;
}
.alert.info {
    background-color: var(--bg-info-solid);
}
.alert.brand {
    background-color: var(--bg-brand-solid);
}
.alert.warning {
    background-color: var(--bg-warning-solid);
}
.alert.success {
    background-color: var(--bg-success-solid);
}
.alert.error {
    background-color: var(--bg-error-solid);

}