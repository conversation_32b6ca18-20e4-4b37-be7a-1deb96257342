<div class="header">
  <div class="left-content">
    <div class="logo-container">
      <img src="/images/jopt/jopt.svg" alt="JOPT Logo" class="logo-image">
    </div>
  </div>
  <div class="exam-name">
    <div *ngIf="currentStep == 1" class=" exam-title">Ki<PERSON><PERSON> tra thiết bị</div>
    <div *ngIf="currentStep != 1" class="exam-title">Kỳ Thi JOPT - 01</div>
  </div>

  <div class="header-icons">
    <div class="icon-wrapper">
      <button (click)="showPopup = true">
        <img src="/images/header/!.png" alt="Alert" width="32" height="32" class="alert-icon">
      </button>
    </div>

    <div class="icon-wrapper">
      <button (click)="toggleSettingPopup()">
        <img src="/images/header/caidat.png" alt="Settings" width="32" height="32" class="settings-icon">
      </button>
    </div>
  </div>
</div>

<!-- /báo bug -->
<div class="popup-overlay" *ngIf="showPopup ">
  <div class="popup-content">
    <div class="popup-header">
      <button class="close-btn" (click)="showPopup = false">×</button>
      <div class="logo">
        <img src="/images/jopt/jopt.svg" alt="JOPT Logo" class="logo-img">
      </div>
    </div>

    <div class="popup-body">
      <h2 class="title">Báo cáo lỗi</h2>
      <p class="description">Chọn lỗi mà bạn gặp phải và báo lại Migii</p>

      <form class="error-form">
        <div class="error-options">
          <label *ngFor="let option of options" class="error-option">
            <input type="radio" name="error" [value]="option" [(ngModel)]="selectedOption" />
            <span class="option-text">{{ option }}</span>
          </label>
        </div>

        <div class="popup-actions">
          <button type="button" class="btn-cancel" (click)="showPopup = false">Hủy bỏ</button>
          <button type="submit" class="btn-submit" [disabled]="!selectedOption" (click)="submitError()">Gửi</button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- nut setting -->
<div  class="setting-popup" *ngIf="showSettingPopup">
  <div class="setting-header">
    <h3>Cài đặt</h3>
  </div>
  <div  class="setting-item">
    <div class="setting-label">
      <span class="setting-title">Cỡ chữ</span>
    </div>
    <div class="font-options">
      <button *ngFor="let size of fontSizes" [class.active]="size === selectedFontSize" (click)="setFontSize(size)"
        class="font-size-btn">
        {{ size }}
      </button>
    </div>
  </div>
</div>

<div style="margin-top: 5%;" *ngIf="currentStep == 1">
  <app-do-exam (senDataCheckDevice)="onNextStep($event)"></app-do-exam>
</div>
<div style="margin-top: 5%;" *ngIf="currentStep == 2">
  <app-tutorial [partExam]="partExam" [exam]="examNo" (dataSentTutorial)="onNextStep($event)"></app-tutorial>
</div>
<div style="margin-top: 5%;" *ngIf="currentStep == 3">
  <app-question [dataExamQuestion]="dataExam" [videoChunkIndex]="this.videoChunkIndex" [data]="mainData"
    (dataSentQuestion)="onNextStep($event)"></app-question>
</div>

<div *ngIf="currentStep == 0" class="login">
  <div class="login-modal-overlay" role="dialog" aria-modal="true" aria-labelledby="modal-title">
  <div class="login-modal">
    <header class="login-modal-header">
      <img
        src="https://api.builder.io/api/v1/image/assets/TEMP/8d29a4f88c88123a03fffbe662d843549ea33b48?width=160"
        alt="JOPT Logo"
        class="login-logo"
      />
      <div class="login-header-content">
        <div class="login-title-container">
          <h1 id="modal-title" class="login-modal-title">Đăng nhập vào phòng thi</h1>
        </div>
        <div class="login-description-container">
          <p class="login-modal-description">
            Chào mừng bạn đến với bài thi đánh giá năng lực nói tiếng Nhật JOPT của Migii JLPT - đơn vị luyện thi tiếng Nhật số 1.
          </p>
        </div>
      </div>
    </header>

<form
  class="login-form"
  role="form"
  aria-labelledby="modal-title"
  (ngSubmit)="onSubmit()"
  #loginForm="ngForm"
>

  <!-- Số báo danh -->
  <div class="login-input-field-container">
    <div class="login-label-container">
      <label class="login-field-label" for="student-id">Số báo danh</label>
      <span class="login-required-asterisk">*</span>
    </div>
    <div class="login-input-wrapper">
      <div class="login-input-content">
        <div class="login-input-icon">
          <!-- SVG icon giữ nguyên -->
        </div>
        <div class="login-typing-area">
          <input
            id="student-id"
            type="text"
            placeholder="Nhập số báo danh của bạn..."
            class="login-input-field"
            [(ngModel)]="studentId"
            name="studentId"
            #studentIdField="ngModel"
            aria-label="Số báo danh"
            aria-required="true"
            required
          />
        </div>
      </div>
    </div>
    <!-- Thông báo lỗi -->
    <span *ngIf="studentIdField.invalid && studentIdField.touched" style="color:red; font-size:13px;">
      Vui lòng nhập số báo danh
    </span>
  </div>

  <!-- Mật khẩu -->
  <div class="login-input-field-container">
    <div class="login-label-container">
      <label class="login-field-label" for="password">Mật khẩu tài khoản</label>
      <span class="login-required-asterisk">*</span>
    </div>
    <div class="login-input-wrapper">
      <div class="login-input-content">
        <div class="login-input-icon">
          <!-- SVG icon giữ nguyên -->
        </div>
        <div class="login-typing-area">
          <input
            id="password"
            [type]="isPasswordVisible ? 'text' : 'password'"
            placeholder="Nhập mật khẩu của bạn..."
            class="login-input-field"
            [(ngModel)]="password"
            name="password"
            #passwordField="ngModel"
            aria-label="Mật khẩu tài khoản"
            aria-required="true"
            required
          />
        </div>
        <button
          type="button"
          class="login-password-toggle"
          (click)="togglePasswordVisibility()"
          [attr.aria-label]="isPasswordVisible ? 'Ẩn mật khẩu' : 'Hiện mật khẩu'"
        >
        <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"
                  class="camera-placeholder-icon">
                  <path
                    d="M8 14C8 11.7909 9.79086 10 12 10H16L18 6H30L32 10H36C38.2091 10 40 11.7909 40 14V34C40 36.2091 38.2091 38 36 38H12C9.79086 38 8 36.2091 8 34V14Z"
                    stroke="#9CA3AF" stroke-width="2" />
                  <circle cx="24" cy="24" r="6" stroke="#9CA3AF" stroke-width="2" />
                </svg>
        </button>
      </div>
    </div>
    <!-- Thông báo lỗi -->
    <span *ngIf="passwordField.invalid && passwordField.touched" style="color:red; font-size:13px;">
      Vui lòng nhập mật khẩu
    </span>
  </div>

  <!-- Nút đăng nhập -->
  <button
    type="submit"
    class="login-button"
    aria-label="Đăng nhập vào phòng thi"
    [disabled]="loginForm.invalid"
  >
    <div class="login-button-content">
      <div class="login-button-text-container">
        <span class="login-button-text">Đăng nhập</span>
      </div>
    </div>
  </button>

</form>


  </div>
</div>
</div>
