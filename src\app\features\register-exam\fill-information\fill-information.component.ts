import { Component } from '@angular/core';
import { SvgIconComponent } from "../../../shared/components/svg-icon/svg-icon.component";
import { FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import * as DATAINIT from '../../../common/constants/register-exam';
import { SelectComponent } from "../../../shared/components/select/select.component";
import { CommonService } from '../../../shared/services/common.service';
import { ModalCropImageComponent } from "../../../shared/modals/modal-crop-image/modal-crop-image.component";
import { SafeUrl } from '@angular/platform-browser';
import { ModalRequirePhotoIdComponent } from "../../../shared/modals/modal-require-photo-id/modal-require-photo-id.component";
import { RegiterExamService } from '../../../shared/services/regiter-exam.service';
import { InforRegisterExam } from '../../../common/interfaces/regiter-exam';


@Component({
    selector: 'app-fill-information',
    standalone: true,
    imports: [
        SvgIconComponent,
        ReactiveFormsModule,
        SelectComponent,
        ModalCropImageComponent,
        ModalRequirePhotoIdComponent
    ],
    templateUrl: './fill-information.component.html',
    styleUrl: './fill-information.component.css'
})
export class FillInformationComponent {
    submitted: boolean = false;
    loadingCccdFront: boolean = false;
    loadingCccdBack: boolean = false;
    loadingImageId: boolean = false;
    registerExam!: FormGroup;
    inforExam!: FormGroup;

    selectedGender: number = 0;
    startPhone: string = '+84';

    dataInit = DATAINIT;

    type: string = '';
    ratio: number = 3 / 4;
    imageEvent: Event | undefined;

    imageCccdFront: Blob | undefined;
    imageCccdBack: Blob | undefined;
    imageId: Blob | undefined;

    imageCccdFrontUrl: SafeUrl = '';
    imageCccdBackUrl: SafeUrl = '';
    imageIdUrl: SafeUrl = '';

    constructor(
        protected readonly commonService: CommonService,
        private registerExamService: RegiterExamService
    ) { }
    ngOnInit() {
        this.registerExam = new FormGroup({
            name: new FormControl('', [Validators.required, Validators.minLength(2), Validators.maxLength(200)]),
            dateOfBird: new FormControl(null, [Validators.required]),
            gender: new FormControl(null, [Validators.required]),
            cccd: new FormControl(null, [Validators.required]),
            email: new FormControl(null, [Validators.required, Validators.email]),
            phone: new FormControl(null, [Validators.required]),
            address: new FormControl(null, [Validators.required]),
            job: new FormControl(null, [Validators.required])
        });

        this.inforExam = new FormGroup({
            test_purpose: new FormControl(null, [Validators.required]),
            jopt_target: new FormControl(null, [Validators.required]),
            referral_source: new FormControl(null, [Validators.required]),
            level_jlpt: new FormControl(null),
            has_taken_jlpt: new FormControl(null, [Validators.required]),
            total_years: new FormControl(null, [Validators.required, Validators.min(0)]),
            total_months: new FormControl(null, [Validators.required, Validators.min(0), Validators.max(11)])
        }, DATAINIT.jlptConditionalValidator('has_taken_jlpt', 'level_jlpt'));
    }

    handleFlagPhoneChange(value: string) {
        this.startPhone = value;
    }

    preCrop(type: string, ratio: number) {
        this.type = type;
        this.ratio = ratio;
    }
    fileChangeEvent(event: Event) {
        const input = event.target as HTMLInputElement;
        if (!input.files || input.files.length === 0) {
            return;
        }
        const file = input.files[0];
        const maxSizeInBytes = 2 * 1024 * 1024;
        if (file.size > maxSizeInBytes) {
            this.commonService.showNotify($localize`:@@txt_file_too_large:File too large. Maximum size is 2MB`, 'error');
            return;
        }
        const allowedTypes = ['image/jpeg', 'image/png'];
        if (!allowedTypes.includes(file.type)) {
            this.commonService.showNotify($localize`:@@txt_invalid_jpg:Invalid format. Only JPG, JPEG, PNG accepted`, 'error');
            return;
        }
        this.imageEvent = event;
    }

    handleCropDone(data: Blob) {
        if (this.type === 'cccd-front') {
            this.imageCccdFront = data;
            this.imageCccdFrontUrl = URL.createObjectURL(data);
            this.uploadCccd(data, 'front');
        } else if (this.type === 'cccd-back') {
            this.imageCccdBack = data;
            this.imageCccdBackUrl = URL.createObjectURL(data);
            this.uploadCccd(data, 'back');
        } else {
            this.imageId = data;
            this.imageIdUrl = URL.createObjectURL(data);
        }
    }

    uploadCccd(data: Blob, type: 'front' | 'back') {
        if (type === 'front') {
            this.loadingCccdFront = true;
        } else {
            this.loadingCccdBack = true;
        }
        this.registerExamService.uploadCccd(data, type).subscribe({
            next: res => {
                if (type === 'front') {
                    this.loadingCccdFront = false;
                } else {
                    this.loadingCccdBack = false;
                }
            },
            error: err => {
                this.commonService.showNotify($localize`:@@txt_has_err:An error occurred, please try again later!`, 'error');
                if (type === 'front') {
                    this.loadingCccdFront = false;
                } else {
                    this.loadingCccdBack = false;
                }
            }
        });
    }


    submit() {
        this.submitted = true;
        console.log(this.registerExam.value);
        if (this.registerExam.invalid || this.inforExam.invalid) {
            return;
        }

        let arrBird: number[] = this.registerExam.value['dateOfBird'].split('-').map((item: string) => Number(item));
        let dataSignup: InforRegisterExam = {
            name: this.registerExam.value['name'],
            gender: this.registerExam.value['gender'],
            phone: this.startPhone + this.registerExam.value['phone'],
            day_of_birth: arrBird[2],
            month_of_birth: arrBird[1],
            year_of_birth: arrBird[0],
            register_info: {
                address: this.registerExam.value['address'],
                number_cccd: this.registerExam.value['cccd'],
                job: this.registerExam.value['job']
            },
            jopt_info: {
                test_purpose: this.inforExam.value['test_purpose'],
                jopt_target: this.inforExam.value['jopt_target'],
                referral_source: this.inforExam.value['referral_source'],
                level_jlpt: this.inforExam.value['has_taken_jlpt'] ? this.inforExam.value['level_jlpt'] : null,
                has_taken_jlpt: this.inforExam.value['has_taken_jlpt'],
                total_years: this.inforExam.value['total_years'],
                total_months: this.inforExam.value['total_months']
            }
        }

        this.registerExamService.changeUserInfor(dataSignup).subscribe({
            next: res => {
                this.commonService.showNotify('Cập nhật thông tin thành công!', 'success');
            }
        });
    }
}
